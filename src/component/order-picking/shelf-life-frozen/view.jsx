import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import Header from '../../common/header';
import Entry from './jsx/entry';
import EntryLocation from './jsx/entry-location';
import DetailPage from './jsx/detail-page';


class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      resultType,
      showDetail,
    } = this.props;
    let Title = '';
    let ShowComponent = '';
    if (showDetail) {
      Title = t('明细');
      ShowComponent = (<DetailPage {...this.props} />);
    } else {
      if (resultType === 1) {
        Title = t('任务领取');
        ShowComponent = (<Entry {...this.props} />);
      } else {
        Title = t('冻结下架');
        ShowComponent = (<EntryLocation {...this.props} />);
      }
    }
    return (
      <div>
        <Header title={Title} style={{ width: '100%' }}>
          {
            resultType === 2 && !showDetail && (
              <span
                style={{ padding: 10 }}
                onClick={() => {
                  store.getUnderInfo();
                  store.changeData({
                    showDetail: true,
                  });
                }}
              >
                {t('明细')}
              </span>
            )
          }
        </Header>
        {ShowComponent}
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  info: PropTypes.shape(),
  resultType: PropTypes.number,
  showDetail: PropTypes.bool,
};

export default i18n(Container);
