.firstItem{
    display: inline-flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 36px;
    line-height: 34px;
    padding: 0 5px;
    box-sizing: border-box;
    padding-left: 10px;
}
.weuiCellsForm{
    box-shadow: 0px 2px 4px 0px rgb(25 122 250 / 15%);
    font-size: 14px;
    padding: 2px;
}
.weight {
    font-size: 16px;
    font-weight: 700;
    color: #141737;
}
.mainInfo{
    width: 100%;
    border-bottom: 1px solid #ddd;
    padding: 10px 0px;
    font-size: 14px;
}
.mainItem{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    height: 36px;
    line-height: 34px;
    padding: 0 10px;
    box-sizing: border-box;
}

.sizeAndColorItem {
    justify-content: flex-end;
}
.mainItem > div{
    font-weight: 700;
}
.btn{
    width: 60px;
    line-height: 20px;
    background-color: #fff;
    border: 1px solid #FF3636;
    border-radius: 3px;
    color: #ff3636;
    font-size: 12px;
}
.inputStyle{
    padding: 0 -1.6em;
}

.orderPicking .weui-cell {
    /*padding: 0!important;*/
}

.boxFloat{
    line-height: 32px;
    height: 32px;
}

.floatLeft{
    float: left;
    padding-left: 10px;
}

.floatRight{
    float: right;
    padding-right: 10px;
}

.inputBox {
    margin: 0 5px;
    padding: 5px;
    width: 80px;
    border: 1px solid #dddddd;
    outline: none;
    border-radius: 3px;
}

.battery {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 111px;
    height: 26px;
    border: 2px solid #679FE7;
}

.battery:after {
    content: '';
    position: absolute;
    top: 5px;
    right: -6px;
    width: 4px;
    height: 12px;
    background-color: #679fe7;
}

.userBank {
    font-size: 15px;
    color: #0059ce;
}

.rankBox {
    display: inline-flex;
    align-items: center;
}

.tipsBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 3px;
    background-color: #d7e3f8;
}

.haveProfit {
    font-size: 13px;
    align-items: center;
    font-weight: bold;
}

.targetProcess {
    color: #333333;
    font-size: 12px;
}

.rankColor {
    color: #feba60;
    font-size: 16px;
}

.symbol {
    position: relative;
    top: -1px;
}

.flexBetween{
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dateStyle{
    font-size: 23px; 
    font-weight: bold;
    color: #d9001b;
}