import { sendPostRequest } from '../../../lib/public-request';

// 下架任务查询
export const getTaskNumApi = (param) => sendPostRequest({
  url: '/validity_freezing_down/query_task',
  param,
}, process.env.WWS_URI);

// 排名查询
export const getRankInfoApi = (param) => sendPostRequest({
  url: '/rank/query',
  param,
}, process.env.WKB);

// 扫描周转箱
export const scanContainerBoxAPI = (param) => sendPostRequest({
  url: '/validity_freezing_down/sacn_container',
  param,
}, process.env.WWS_URI);

// 扫描库位号
export const scanLocationAPI = (param) => sendPostRequest({
  url: '/validity_freezing_down/scan_location',
  param,
}, process.env.WWS_URI);

// 扫描商品条码
export const scanGoodsAPI = (param) => sendPostRequest({
  url: '/validity_freezing_down/scan_goods',
  param,
}, process.env.WWS_URI);

// 关箱
export const closeContainerApi = (param) => sendPostRequest({
  url: '/validity_freezing_down/close_pick_container',
  param,
}, process.env.WWS_URI);

// 提交数据
export const commitAPI = (param) => sendPostRequest({
  url: '/validity_freezing_down/commit',
  param,
}, process.env.WWS_URI);

// 短拣
export const shortPickAPI = (param) => sendPostRequest({
  url: '/validity_freezing_down/short_pick',
  param,
}, process.env.WWS_URI);

// 获取下架信息
export const underInfoAPI = (param) => sendPostRequest({
  url: '/validity_freezing_down/under_info',
  param,
}, process.env.WWS_URI);
