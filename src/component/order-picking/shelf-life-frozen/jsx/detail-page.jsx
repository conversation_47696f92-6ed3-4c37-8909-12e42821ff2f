import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import { NavDetail, pages, Footer } from '../../../common';
import store from '../reducers';

const { View } = pages;

const rows1 = [
  [
    {
      title: t('SKC'),
      render: 'goodsSn',
    },
    {
      title: t('数量'),
      render: 'alreadyNum',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('尺码'),
      render: 'size',
    },
    {
      title: t('库位'),
      render: row => (
        <div>
          <span style={{ color: '#666c7c', marginRight: 7 }}>{t('库位')}</span>
          <span style={{ color: 'red' }}>
            <span>{row.location}</span>
          </span>
        </div>
      ),
    },
  ],
  [
    {
      title: t('周转箱'),
      render: 'containerCode',
    },
  ],
];
const rows2 = [
  [
    {
      title: t('SKC'),
      render: 'goodsSn',
    },
    {
      title: t('数量'),
      render: 'waitNum',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('尺码'),
      render: 'size',
    },
    {
      title: t('库位'),
      render: row => (
        <div>
          <span style={{ color: '#666c7c', marginRight: 7 }}>{t('库位')}</span>
          <span style={{ color: 'red' }}>
            <span>{row.location}</span>
          </span>
        </div>
      ),
    },
  ],
];

const DetailPage = (props) => {
  const {
    underInfo,
  } = props;
  const { alreadyUnderGoodsInfo, waitUnderGoodsInfo } = underInfo;
  const alreadyList = alreadyUnderGoodsInfo || [];
  const waitList = waitUnderGoodsInfo || [];
  const detailList = [
    [...alreadyList],
    [...waitList],
  ];
  const rowsList = [
    rows1, rows2,
  ];
  const totalNumList = [underInfo.alreadyPickNum, underInfo.waitPickNum];
  const navList = [t('已拣选'), t('未拣选')];
  return (
    <View diff={100}>
      <NavDetail
        data={detailList}
        rowsList={rowsList}
        navList={navList}
        totalNumList={totalNumList}
        imgUrlFieldName="imageUrl"
      />
      <Footer
        beforeBack={() => {
          store.changeData({
            showDetail: false,
            resultType: 2,
          });
        }}
      />
    </View>
  );
};

DetailPage.propTypes = {
  underInfo: PropTypes.shape(),
};

export default DetailPage;
