import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import RowInfo from '../../../common/row-info';

const Info = (props) => {
  const {
    downNum,
    waitTaskNum,
    rankInfo,
  } = props;

  const baseData = [
    {
      label: t('待领取任务数'),
      content: waitTaskNum,
      type: 'info',
    },
    {
      label: t('已下架件数'),
      content: downNum,
      type: 'warn',
    },
    {
      label: t('排名'),
      content: `${rankInfo.total}/${rankInfo.rank}`,
      type: 'sky',
    },
  ];

  return (
    <Form>
      {baseData.map(item => (
        <RowInfo
          key={item.label}
          extraStyle={{
            borderBottom: 'none',
          }}
          label={item.label}
          content={item.content}
          type={item.type}
        />
      ))}
    </Form>
  );
};

Info.propTypes = {
  downNum: PropTypes.number,
  waitTaskNum: PropTypes.number,
  rankInfo: PropTypes.shape(),
};

export default Info;
