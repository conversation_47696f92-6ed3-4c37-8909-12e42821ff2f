import React from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import RowInfo from '../../../common/row-info';
import store from '../reducers';
import Modal from '../../../common/modal';
import Footer from '../../../common/footer';
import FooterBtn from '../../../common/footer-btn';
import FocusInput from '../../../common/focus-input';
import { classFocus } from '../../../../lib/util';
import style from '../style.css';

const EntryLocation = (props) => {
  const {
    scanedContainerInfo: data,
    location,
    dtlContainerDisabled,
    containerDisabled,
    goodsSnPrintDisabled,
    offNumDisabled,
    goodsSnPrint,
    offNum,
    locationDisabled,
    inputContainerCode,
    scanLocationDisable,
  } = props;

  return (
    <div>
      <Form>
        <div className={style.firstItem}>
          <span>{data.replenishmentCode}</span>
          <div>
            {t('已拣/总件数：')}
            <span className={style.weight}>
              <span>{data.hadOffNum}</span>
              /{data.goodsTotalNum}
            </span>
          </div>
        </div>
      </Form>
      <div className={style.mainInfo}>
        <div className={style.boxFloat}>
          <div className={style.floatLeft}>{t('库位号')}</div>
          <div className={style.floatRight}>
            <span
              style={
                  {
                    fontSize: 16,
                    fontWeight: 'normal',
                  }
                }
              dangerouslySetInnerHTML={{ __html: data.location.replace(/(.{6})(.{0,7})(.*)/, '$1<b style="font-size: 23px ">$2</b>$3') }}
            />
            <span style={{ color: '#D9001B', fontSize: 20, fontWeight: 700 }}>
              {data.isLocation ? `--${data.sequence}` : ''}
            </span>
          </div>
        </div>
        <div className={style.mainItem} style={{ lineHeight: '32px', height: 32 }}>
          <span>{t('SKC')}</span>
          <div style={{ color: '#D9001B', fontSize: 16 }}>
            {data.goodsSn}
          </div>
        </div>
        <div className={classnames(style.mainItem, style.sizeAndColorItem)}>
          <div
            style={{
              fontSize: 23,
              fontWeight: 'bold',
              color: '#D9001B',
              marginRight: 50,
            }}
          >
            {data.color}
          </div>
          <div style={{ fontSize: 23, fontWeight: 'bold', color: '#D9001B' }}>
            {data.size}
          </div>
        </div>
        <div style={{ padding: '0px 10px' }}>
          <div className={style.flexBetween}>
            <span>{t('生产日期')}</span>
            <span className={style.dateStyle}>{data.productionDate}</span>
          </div>
          <div className={style.flexBetween}>
            <span>{t('到期日期')}</span>
            <span className={style.dateStyle}>{data.expiringDate}</span>
          </div>
        </div>
      </div>
      <div className={style.weuiCellsForm}>
        <RowInfo
          extraStyle={{
            borderBottom: 'none',
          }}
          textExtraStyle={{
            width: '32%',
            fontWeight: 'bold',
          }}
          label={t('待拣数量')}
          content={<span>{data.waitOffNum}</span>}
        />
      </div>
      <Form style={{ marginTop: '10px' }}>
        <FocusInput
          data-bind="inputContainerCode"
          disabled={dtlContainerDisabled || containerDisabled === 0}
          className="containerCode"
          lineBreak={false}
          onPressEnter={() => {
            if (inputContainerCode) {
              store.scanContainer(inputContainerCode);
            }
          }}
        >
          <label>{t('周转箱')}：</label>
        </FocusInput>

        <FocusInput
          data-step="2"
          data-intro={t('第二步，扫描库位号')}
          placeholder={t('请扫描库位号')}
          data-bind="location"
          className="location"
          disabled={locationDisabled === 0 || !dtlContainerDisabled}
          lineBreak={false}
          autoFocus
          onPressEnter={() => {
            if (location) {
              store.scanLocation(location);
            }
          }}
        >
          <label>
            {t('库位号')}：
          </label>
        </FocusInput>
        <FocusInput
          data-step="3"
          data-intro={t('第三步，输入数量')}
          placeholder={t('请输入数量')}
          disabled={offNumDisabled === 0 || !dtlContainerDisabled}
          // value={offNum}
          data-bind="offNum"
          className="offNum"
          lineBreak={false}
          // onChange={(e) => {
          //   const { value } = e.target;
          //   const reg = /^([0-9]*)$/;
          //   console.log('value', value);
          //   if ((!Number.isNaN(value) && reg.test(value)) || value === '') {
          //     store.changeData({
          //       offNum: Number(value),
          //     });
          //   }
          // }}
          onPressEnter={() => {
            const reg = /^([0-9]+)$/;
            const offNumValue = Number(offNum);
            if (Number.isNaN(offNumValue) || !reg.test(offNum) || (offNumValue <= 0)) {
              Modal.error({
                content: t('请输入需要下架的件数'),
                onOk: classFocus('offNum'),
              });
              store.changeData({
                offNum: '',
              });
            } else {
              classFocus('goodsSnPrint');
            }
          }}
        >
          <label>
            {t('数量')}：
          </label>
        </FocusInput>
        <FocusInput
          data-step="4"
          data-intro={t('第四步，扫描商品条码')}
          placeholder={t('请扫描商品条码')}
          disabled={goodsSnPrintDisabled === 0 || !scanLocationDisable || !dtlContainerDisabled}
          data-bind="goodsSnPrint"
          className="goodsSnPrint"
          lineBreak={false}
          onPressEnter={() => {
            if (goodsSnPrint) {
              store.scanGoods(goodsSnPrint);
            }
          }}
        >
          <label>
            {t('商品条码')}：
          </label>
        </FocusInput>
      </Form>
      <Footer
        beforeBack={() => {
          store.init();
          store.initInfoData();
          store.changeData({
            resultType: 1,
            dtlContainerDisabled: true,
          });
        }}
      >
        <FooterBtn
          onClick={() => {
            Modal.confirm2({
              title: t('短拣'),
              content: (
                <div>
                  <div>{t('短拣明细如下，是否继续？')}</div>
                  <div style={{ textAlign: 'left', padding: '10px', lineHeight: '20px' }}>
                    <div>{t('库位号')}：{data.location}</div>
                    <div>SKC：{data.goodsSn}</div>
                    <div>{t('尺码')}：{data.size}</div>
                    <div>{t('件数')}：{data.waitOffNum}</div>
                  </div>
                </div>
              ),
              onOk: () => {
                store.shortPick();
              },
              onCancel: () => {
                store.changeData({
                  data: {
                    bigContainerCode: '',
                  },
                });
                classFocus('bigContainerCode');
              },
            });
          }}
        >
          {t('短拣')}
        </FooterBtn>
      </Footer>
    </div>
  );
};

EntryLocation.propTypes = {
  scanedContainerInfo: PropTypes.shape(),
  location: PropTypes.string,
  dtlContainerDisabled: PropTypes.bool,
  containerDisabled: PropTypes.number,
  goodsSnPrintDisabled: PropTypes.number,
  offNumDisabled: PropTypes.number,
  goodsSnPrint: PropTypes.string,
  offNum: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  locationDisabled: PropTypes.number,
  inputContainerCode: PropTypes.string,
  scanLocationDisable: PropTypes.bool,
};

export default EntryLocation;
