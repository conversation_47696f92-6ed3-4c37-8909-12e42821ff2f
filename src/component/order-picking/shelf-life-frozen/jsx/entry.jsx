import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import Footer from '../../../common/footer';
import FocusInput from '../../../common/focus-input';
import Info from './info';

const Entry = (props) => {
  const {
    inputContainerCode,
    containerDisabled,
  } = props;
  return (
    <div>
      <div>
        <Info {...props} />
        <Form>
          <FocusInput
            placeholder={t('请扫描周转箱')}
            autoFocus
            data-bind="inputContainerCode"
            disabled={containerDisabled === 0}
            className="containerCode"
            lineBreak={false}
            onPressEnter={() => {
              if (inputContainerCode) {
                store.scanContainer(inputContainerCode);
              }
            }}
          >
            <label>{t('周转箱')}</label>
          </FocusInput>
        </Form>
      </div>
      <Footer />
    </div>
  );
};

Entry.propTypes = {
  inputContainerCode: PropTypes.string,
  containerDisabled: PropTypes.number,
};

export default Entry;
