import assign from 'object-assign';
import { markStatus } from 'rrc-loader-helper';
// import message from 'common/message';
import { t } from '@shein-bbl/react';
import {
  scanContainerBoxAPI,
  getRankInfoApi,
  scanGoodsAPI,
  getTaskNumApi,
  scanLocationAPI,
  closeContainerApi,
  commitAPI,
  shortPickAPI,
  underInfoAPI,
} from './server';
import Modal from '../../common/modal';
import { classFocus } from '../../../lib/util';

const defaultInfo = {
  location: '', // 库位号
};
const defaultState = {
  containerCode: '', // 周转箱
  downNum: undefined, // 已下架件数
  waitTaskNum: undefined, // 待领任务数
  rankInfo: { // 排名数据
    rank: 0,
    total: 0,
  },
  resultType: 1, // 1领取页面 2下架页面
  scanedContainerInfo: defaultInfo, // 扫描周转箱后返回的数据
  location: '', // 库位号
  goodsSnPrint: '', // 商品条码
  containerDisabled: 1,
  locationDisabled: 1,
  goodsSnPrintDisabled: 1,
  offNumDisabled: 1,
  dtlContainerDisabled: true,
  detailList: [
    [],
    [],
  ],
  showDetail: false,
  inputContainerCode: '',
  underInfo: {
    alreadyPickNum: 0, // 已拣选总件数
    waitPickNum: 0, // 未拣选总件数
    alreadyUnderGoodsInfo: [],
    waitUnderGoodsInfo: [],
  },
  scanLocationDisable: false,
};

export default {
  state: defaultState,

  $init: () => defaultState,

  * init() {
    // 获取排名
    yield this.getRankInfo();
    // 下架任务查询
    yield this.getTaskNum();
  },

  /**
   * 获取排名
   */
  * getRankInfo() {
    const res = yield getRankInfoApi({
      rankTypeCode: 18,
    });
    if (res.code === '0') {
      yield this.changeData({ rankInfo: res.info });
    } else {
      Modal.error({ content: res.msg });
    }
  },

  /**
   * 下架任务查询
   */
  * getTaskNum() {
    const res = yield getTaskNumApi();
    if (res.code === '0') {
      yield this.changeData({
        downNum: res.info.downNum, // 已下架件数
        waitTaskNum: res.info.waitTaskNum, // 待领任务数
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },


  changeData(state, data) {
    assign(state, data);
  },

  /**
   * 更新周转箱数据
   * @param {object} info
   */
  * updateInfoData(info) {
    yield this.changeData({
      scanedContainerInfo: info,
      location: '', // 库位号
      goodsSnPrint: '', // 商品条码
    });
    classFocus('containerCode');
  },

  /**
   * 初始化大箱数据
   */
  * initInfoData() {
    yield this.changeData({
      scanedContainerInfo: defaultState,
      location: '', // 库位号
      goodsSnPrint: '', // 商品条码
      containerCode: '',
      inputContainerCode: '',
    });
    classFocus('containerCode');
  },

  /**
   * 扫描周转箱
   * @param {string} containerCode 周转箱
   */
  * scanContainer(containerCode) {
    markStatus('containerDisabled');
    const result = yield scanContainerBoxAPI({ containerCode });
    const {
      info, msg, code,
    } = result;
    if (code === '0') {
      yield this.changeData({
        scanedContainerInfo: {
          ...info,
        },
        offNum: info.waitOffNum,
        resultType: 2, // 跳转到下架页面
        dtlContainerDisabled: true,
      });
      // 焦距到库位号输入框
      classFocus('location');
    } else {
      yield this.changeData({
        inputContainerCode: '',
      });
      Modal.error({ content: msg, className: 'containerCode' });
    }
  },

  /**
   * 扫描库位号
   * @param {string} location 库位号
   * @returns
   */
  * scanLocation(location) {
    markStatus('locationDisabled');
    const { scanedContainerInfo, inputContainerCode } = yield '';
    const {
      goodsSn, replenishmentCode, size, skuCode,
    } = scanedContainerInfo;
    const param = {
      containerCode: inputContainerCode,
      goodsSn,
      location,
      replenishmentCode,
      size,
      skuCode,
    };
    const result = yield scanLocationAPI(param);
    const {
      info, msg, code,
    } = result;
    if (code === '0') {
      yield this.changeData({
        scanLocationDisable: true,
      });
      // type（1、正常往下走，2、弹框是否关箱，3、货位存在不同生产日期商品，弹框提示）
      const { type } = info;
      if (type === 2) {
        const status = yield new Promise(r => Modal.confirm({
          title: t('确认关箱?'),
          onOk: () => r(true),
          onCancel: () => r(false),
        }));
        if (status) {
          yield this.closeContainer();
        } else {
          yield this.changeData({
            location: '',
            scanLocationDisable: false,
          });
          classFocus('location');
        }
        return;
      }
      if (type === 3) {
        Modal.info({ content: msg, className: 'goodsSnPrint' });
        return;
      }
      // 焦距到商品条码输入框
      classFocus('goodsSnPrint');
    } else {
      // 503001 货位存在不同生产日期商品，弹框提示
      if (code === '503001') {
        yield this.changeData({
          scanLocationDisable: true,
        });
        Modal.info({ content: msg, className: 'goodsSnPrint' });
        return;
      }
      yield this.changeData({
        location: '',
      });
      Modal.error({ content: msg, className: 'location' });
    }
  },


  // 关箱
  * closeContainer() {
    const { scanedContainerInfo, inputContainerCode } = yield '';
    const { shiftOrderCode } = scanedContainerInfo;
    const param = {
      containerCode: inputContainerCode, // 周转箱号
      shiftOrderCode, // 移位单号
    };
    const res = yield closeContainerApi(param);
    if (res.code === '0') {
      yield this.changeData({
        inputContainerCode: '',
        location: '',
        goodsSnPrint: '',
        dtlContainerDisabled: false,
      });
      classFocus('containerCode');
    } else {
      Modal.error({ content: res.msg, className: 'location' });
    }
  },

  /**
   * 扫描商品条码
   * @param {string} goodsSnPrint
   * @returns
   */
  * scanGoods(goodsSnPrint) {
    markStatus('goodsSnPrintDisabled');
    const { scanedContainerInfo, inputContainerCode, location } = yield '';
    const {
      goodsSn, size, skuCode,
    } = scanedContainerInfo;
    const reqParam = {
      containerCode: inputContainerCode,
      goodsSn,
      goodsSnPrint,
      size,
      skuCode,
      location,
    };
    const result = yield scanGoodsAPI(reqParam);
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      const {
        type,
        msg: infoMsg,
      } = info;
      yield this.changeData({
        scanLocationDisable: false,
      });
      // type （1、正常往下走，2、弹框是否关箱，3、商品不能混放，要换箱，4、周转箱超过品项，弹框
      if (type === 2) {
        const status = yield new Promise(r => Modal.confirm({
          title: t('确认关箱?'),
          onOk: () => r(true),
          onCancel: () => r(false),
        }));
        if (status) {
          yield this.closeContainer();
        } else {
          yield this.changeData({
            location: '',
            goodsSnPrint: '',
          });
          classFocus('location');
        }
        return;
      }
      if ([3, 4].includes(type)) {
        const status = yield new Promise(r => Modal.info({
          title: infoMsg,
          onOk: () => r(true),
        }));
        if (status) {
          // 光标置于商品条码文本框并清空内容
          yield this.changeData({
            goodsSnPrint: '',
            scanLocationDisable: true,
          });
          classFocus('goodsSnPrint');
        }
        return;
      }
      yield this.commit();
    } else {
      yield this.changeData({
        goodsSnPrint: '',
        scanLocationDisable: true,
      });
      Modal.error({ content: msg, className: 'goodsSnPrint' });
    }
  },

  /**
   * 提交数据
   */
  * commit() {
    markStatus('offNumDisabled');
    const {
      scanedContainerInfo, goodsSnPrint, location, inputContainerCode, offNum,
    } = yield '';
    const {
      goodsSn, size, skuCode, shiftOrderCode, replenishmentCode,
    } = scanedContainerInfo;
    const reqParam = {
      containerCode: inputContainerCode,
      goodsSn,
      goodsSnPrint,
      location,
      offNum,
      shiftOrderCode,
      replenishmentCode,
      size,
      skuCode,
    };
    const result = yield commitAPI(reqParam);
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      const {
        containerCode: nextContainerCode,
      } = info;
      if (nextContainerCode) {
        const status = yield new Promise(r => Modal.success({
          content: t('下架成功，下架空周转箱：{}', nextContainerCode),
          onOk: () => r(true),
        }));
        if (status) {
          yield this.handleCommit(result);
        }
      } else {
        yield this.handleCommit(result);
      }
    } else {
      yield this.changeData({
        goodsSnPrint: '',
        scanLocationDisable: true,
      });
      Modal.error({ content: msg, className: 'goodsSnPrint' });
    }
  },
  /**
   * 统一处理提交场景
   * @param {object} result
   * @returns
   */
  * handleCommit(result) {
    const {
      info,
    } = result;
    const { scanedContainerInfo, location } = yield '';
    // type 1、任务未完成，继续下一个明细， 5、任务下架完成
    const { taskGoodsInfo, type } = info;
    if (type === 1) {
      // 比对“当前库位”与”下一个库位”
      if (location === taskGoodsInfo.location) {
        // 清空条码
        yield this.changeData({
          goodsSnPrint: '',
          scanedContainerInfo: {
            ...scanedContainerInfo,
            ...taskGoodsInfo,
          },
          scanLocationDisable: true,
        });
        classFocus('goodsSnPrint');
      } else {
        // 清空条码和库位信息
        yield this.changeData({
          goodsSnPrint: '',
          location: '',
          scanedContainerInfo: {
            ...scanedContainerInfo,
            ...taskGoodsInfo,
          },
        });
        classFocus('location');
      }
      return;
    }
    if (type === 5) {
      // 返回领取任务页面
      yield this.changeData({
        resultType: 1,
        dtlContainerDisabled: true,
      });
      yield this.initInfoData();
      yield this.init();
      return;
    }
    yield this.changeData({
      scanLocationDisable: true,
    });
    classFocus('goodsSnPrint');
  },
  /**
   * 短拣
   */
  * shortPick() {
    const { scanedContainerInfo, inputContainerCode } = yield '';
    const {
      shiftOrderCode, replenishmentCode, waitOffNum, location,
    } = scanedContainerInfo;
    const reqParam = {
      countNum: waitOffNum, // 短拣点数(业务数出的真实的库存数)
      location,
      shiftOrderCode,
      replenishmentCode,
      containerCode: inputContainerCode,
    };
    const result = yield shortPickAPI(reqParam);
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      const {
        taskGoodsInfo,
        type,
        containerCode: nextContainerCode,
      } = info;
      // type 1、下架未完成 2、下架完成
      if (type === 1) {
        // 焦点定位库位  清空条码，库位
        yield this.changeData({
          goodsSnPrint: '',
          location: '',
          scanedContainerInfo: {
            ...scanedContainerInfo,
            ...taskGoodsInfo,
          },
        });
        classFocus('location');
      } else {
        // 任务的下架件数总和是否等于0
        if (!nextContainerCode) {
          yield this.goBackToEntry();
        } else {
          const status = yield new Promise(r => Modal.info({
            title: t('短拣成功，请下架空周转箱：{}', nextContainerCode),
            onOk: () => r(true),
          }));
          if (status) {
            yield this.goBackToEntry();
          }
        }
      }
    } else {
      Modal.error({ content: msg });
    }
  },
  /**
   * 返回到领取页面
   */
  * goBackToEntry() {
    const status = yield new Promise(r => Modal.info({
      title: t('任务已结束，点击确定返回任务领取页面'),
      onOk: () => r(true),
    }));
    if (status) {
      yield this.changeData({
        resultType: 1,
      });
      yield this.initInfoData();
      yield this.init();
    }
  },
  /**
   * 获取下架信息
   */
  * getUnderInfo() {
    const { scanedContainerInfo } = yield '';
    const {
      shiftOrderCode, replenishmentCode,
    } = scanedContainerInfo;
    const reqParam = {
      shiftOrderCode,
      replenishmentCode,
    };
    const result = yield underInfoAPI(reqParam);
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      yield this.changeData({
        underInfo: info,
      });
    } else {
      Modal.error({ content: msg });
    }
  },
};
