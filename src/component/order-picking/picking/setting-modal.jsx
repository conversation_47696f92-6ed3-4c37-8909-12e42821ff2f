import React from 'react';
import PropTypes from 'prop-types';
import { Dialog } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import { SESSION_PICK_SEQ_VAL } from 'lib/storage';
import store from './reducers';
import { SwitchIcon } from '../../common';

function SettingModal(props) {
  const {
    showSettingModal,
    pickSeq,
    oldPickSeq,
    match: { params: { status } },
    takePickTaskParam,
  } = props;

  return (
    <Dialog
      title={`${t('拣货配置')}`}
      show={showSettingModal}
      buttons={[{
        label: t('取消'),
        type: 'default',
        onClick: () => {
          // 取消更改
          store.changeData({ data: { showSettingModal: false, pickSeq: oldPickSeq } });
        },
      }, {
        label: t('确认'),
        type: 'primary',
        onClick: () => {
          if (pickSeq !== oldPickSeq) {
            // 将更改存到sesstion
            sessionStorage.setItem(SESSION_PICK_SEQ_VAL, pickSeq);
            // 如果页面为领取的任务明细，刷新任务明细，重新领取一条任务明细，刷新库位预告
            if (status === 'picking-page') {
              store.takePickTask({
                data: { ...takePickTaskParam, noTipModal: true },
              });
            }
          }
          store.changeData({ data: { showSettingModal: false } });
        },
      }]}
    >
      <div
        style={{ padding: '20px 0 80px 0', textAlign: 'left' }}
        onClick={() => {
          store.changeData({ data: { pickSeq: pickSeq === 1 ? 0 : 1 } });
        }}
      >
        <span>{t('正序拣货')}：</span>
        <span style={{ position: 'relative', top: -2, marginLeft: 46 }}>
          <SwitchIcon
            value={pickSeq === 0}
            content={[t('开'), t('关')]}
          />
        </span>
      </div>
    </Dialog>
  );
}
SettingModal.propTypes = {
  showSettingModal: PropTypes.bool,
  pickSeq: PropTypes.number,
  oldPickSeq: PropTypes.number,
  match: PropTypes.shape(),
  takePickTaskParam: PropTypes.shape(),
};

export default SettingModal;
