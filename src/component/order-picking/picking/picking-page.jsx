import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import {
  Form, Button, Toast,
} from 'react-weui/build/packages';
import UserIntro from 'common/user-intro';
import { push } from 'react-router-redux';
import { LOCAL_PICKING_PAGE_CODE_OBJ, LOCAL_WAREHOUSE } from 'lib/storage';
import Footer from '../../common/footer';
import IntroContext from '../../user-intro-page/IntroContext';
import FocusInput from '../../common/focus-input';
import FooterBtn from '../../common/footer-btn';
import Info from './Info';
import Modal from '../../common/modal';
import RowInfo from '../../common/row-info';
import store from './reducers';
import error from '../../../source/audio/delete.mp3';
import warnSound from '../../../source/audio/warn.mp3';
import { classFocus } from '../../../lib/util';
import Header from '../../common/header';
import { View } from '../../common';
import style from './style.css';
import message from '../../common/message';
import navStore from '../../nav/reducers';

const audio = new Audio(error);
audio.load();

const audioWarn = new Audio(warnSound);
audioWarn.loop = true;
audioWarn.load();
const playAudio = () => {
  audioWarn.play();
  setTimeout(() => audioWarn.pause(), 5000);
};
let warningTimer = null;
let warningTimer2 = null;
const onEnd = (oldTime, step) => {
  playAudio();
  if (warningTimer === null) {
    if (oldTime < 0) {
      warningTimer2 = setTimeout(() => {
        playAudio();
        warningTimer = setInterval(() => playAudio(), step * 1000);
      }, (step - (-oldTime % 60)) * 1000);
    } else {
      warningTimer = setInterval(() => playAudio(), step * 1000);
    }
  }
};
const onSoon = () => {
  playAudio();
};

class PickingPage extends React.Component {
  componentDidMount() {
    this.init();
    const pickingPageCodeObj = JSON.parse(localStorage.getItem(LOCAL_PICKING_PAGE_CODE_OBJ) || '{}');
    if (pickingPageCodeObj.pickContainerCode && document.URL.indexOf('picking-page') !== -1) {
      store.changeData({
        data: {
          pickingPageCodeObj,
        },
      });
      // 没有校验箱号 禁止回退
      window.history.pushState(null, null, document.URL);
      window.addEventListener('popstate', (e) => {
        // 微前端劫持触发的不执行，防止死循环
        if (e?.singleSpa) {
          return;
        }
        // 优化：防止影响其它页面返回
        if (pickingPageCodeObj.pickContainerCode && document.URL.indexOf('picking-page') !== -1) {
          window.history.pushState(null, null, document.URL);
        }
      });
    }
    navStore.changeData({ data: { pageStore: store } });
    store.getOpenPicturePickingModel();
  }

  componentDidUpdate() {
    navStore.changeData({ data: { pageStore: store } });
  }

  componentWillUnmount() {
    if (warningTimer) {
      clearInterval(warningTimer);
      warningTimer = null;
    }
    if (warningTimer2) {
      clearTimeout(warningTimer2);
      warningTimer2 = null;
    }
  }

  /**
   * 初始一些函数
   */
  init() {
    const {
      dispatch,
      match: { params: { pickType } },
    } = this.props;

    this.goBack = () => {
      store.init();
      dispatch(push(`/order-picking/picking/get-task/${pickType}`));
    };
    this.pickContainerCodeCheck = (val) => {
      if (val) {
        store.changeData({
          data: {
            pickContainerCodeCheck: val,
          },
        });
      } else {
        audio.play();
        message.error(t('请扫描正确的核对箱号'), 2000);
        document.querySelectorAll('.pickContainerCodeCheck')?.forEach((element) => {
          element.value = '';
        });
        classFocus('pickContainerCodeCheck');
        store.changeData({
          data: {
            pickContainerCodeCheck: '',
          },
        });
      }
    };
    this.pickContainerCodeFocus = () => {
      requestAnimationFrame(() => {
        classFocus('pickContainerCode');
      });
    };
  }

  render() {
    const {
      dispatch,
      pickContainerCodeDisabled,
      goodsBarcode,
      locationInput,
      locationValid,
      goodsBarcodeValid,
      info,
      timeoutInfo,
      match: { params: { pickType } },
      nextLocations,
      dataLoading,
      nationalLineTypeName,
      collectTips,
      wellenTips,
      wellenTypeName,
      pickSeq,
      steps,
      openPicturePickingModel,
      lastFocus,
    } = this.props;
    const {
      goodsSn,
      location,
      pickContainerCode,
      taskCode,
      taskDetailId,
      size,
      skuCode,
      pickNum,
      isLocation,
      sequence,
      isCombine,
      urgentMessage,
      totalPickNum,
    } = info;

    const renderTitle = () => {
      // 任务模式,0-单任务，1-多任务，2-合并任务
      if (Number(info.taskMode) !== 2) {
        return (
          <RowInfo
            extraStyle={{
              justifyContent: 'flex-start',
              marginRight: 0,
            }}
            textExtraStyle={{
              width: '32%',
              color: '#FF0000',
              fontWeight: 'bold',
              fontSize: 20,
            }}
            label={`${t('库位待拣')}/${t('库位总需拣')}`}
            content={(
              <span>{`${pickNum || ''} `}
                <span
                  style={{ color: '#616161' }}
                >/{totalPickNum}
                </span>
                {`${isCombine ? urgentMessage : ''}`}
              </span>
            )}
          />
        );
      } else {
        if (pickType === '1') {
          return (
            <RowInfo
              extraStyle={{
                justifyContent: 'flex-start',
                marginRight: 0,
              }}
              textExtraStyle={{
                width: '32%',
                color: '#FF0000',
                fontWeight: 'bold',
                fontSize: 20,
              }}
              label={`${t('待拣数')}/${t('总件数')}`}
              content={(
                <span>{`${pickNum || ''} `}
                  <span
                    style={{ color: '#616161' }}
                  >/{totalPickNum}
                  </span>
                  {`${isCombine ? urgentMessage : ''}`}
                </span>
              )}
            />
          );
        } else {
          return (
            <RowInfo
              extraStyle={{
                justifyContent: 'flex-start',
                marginRight: 0,
              }}
              textExtraStyle={{
                width: '32%',
                color: '#FF0000',
                fontWeight: 'bold',
                fontSize: 20,
              }}
              label={t('待拣数量')}
              content={<span>{`${pickNum || ''}  ${isCombine ? urgentMessage : ''}`}</span>}
            />
          );
        }
      }
    };

    return (
      <IntroContext.Consumer>
        {(context) => (
          <div>
            <Header
              title={pickType === '1' ? t('正常拣货') : t('异常拣货')}
              homeIcon={pickType !== '1'}
            >
              <div>
                <span
                  onClick={(e) => {
                    // 库位号为空，或者扫描/输入内容不是正确的拣货库位 短拣置灰不能点击
                    if (!locationInput || locationInput !== location) {
                      return;
                    }
                    // loading 状态不能点击
                    if (goodsBarcodeValid) {
                      return;
                    }
                    Modal.confirm({
                      content: t('确认是否短拣当前商品'),
                      onOk: () => new Promise((res) => {
                        store.shortPick({
                          data: {
                            param: {
                              location,
                              pickContainerCode,
                              pickExceptionNum: isLocation ? 1 : pickNum,
                              taskCode,
                              taskDetailId,
                              pickType,
                            },
                            goBack: this.goBack,
                            pickContainerCodeCheck: this.pickContainerCodeCheck,
                            ref: this.locationRef,
                            res,
                          },
                        });
                      }),
                    });
                    e.preventDefault();
                  }}
                  style={{ color: (locationInput && location === locationInput) && !goodsBarcodeValid ? '#fff' : '#999' }}
                >
                  <Icon name="duanjian" style={{ marginRight: 5 }} />
                  {t('短拣')}
                </span>
                {
                  pickType === '1' && (
                    <span
                      style={{ position: 'absolute', left: -240, padding: '0 10px' }}
                      onClick={() => {
                        store.changeData({
                          data: {
                            showSettingModal: true,
                            oldPickSeq: pickSeq,
                          },
                        });
                      }}
                    >
                      <Icon style={{ fontSize: 20 }} name="setting" />
                    </span>
                  )
                }
              </div>
            </Header>
            {!openPicturePickingModel && (<Toast icon="loading" show={dataLoading === 0}>Loading...</Toast>)}
            {!openPicturePickingModel && (
              <UserIntro
                showIntro={context.showIntroVal}
                steps={steps}
                finishHandle={() => {
                  dispatch(push(`/order-picking/picking/get-task/${pickType}`));
                  store.getPickNum({
                    data: {
                      pickType,
                    },
                  });
                }}
              />
            )}
            <View
              diff={100}
              flex={false}
            >
              <Form>
                <Info
                  timeoutInfo={timeoutInfo}
                  onSoon={onSoon}
                  onEnd={onEnd}
                  {...this.props}
                />
                {renderTitle()}
                {/* 是否开启图片拣货模式 */}
                {!openPicturePickingModel && (
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      padding: '0px 16px',
                      fontSize: 16,
                      lineHeight: '35px',
                      height: 35,
                    }}
                  >
                    {
                      (nextLocations && nextLocations.length > 0) ? (
                        <>
                          {
                            nextLocations.map((item, index) => (
                              <div
                                key={item}
                                style={{ fontWeight: index === 0 ? 'bold' : '' }}
                              >{item.slice(6)}
                              </div>
                            ))
                          }
                        </>
                      ) : ''
                    }
                  </div>
                )}
              </Form>
              <Form
                style={{ marginTop: '10px', boxShadow: 'none' }}
              >
                {/* 是否开启图片拣货模式 */}
                {!openPicturePickingModel && (
                  <FocusInput
                    value={pickContainerCode}
                    className="pickContainerCode"
                    importance
                    onChange={(e) => {
                      store.changeCurrent({
                        data: {
                          pickContainerCode: e.target.value.trim(),
                        },
                      });
                    }}
                    ref={(node) => {
                      this.pickContainerCodeRef = node;
                    }}
                    onPressEnter={(e) => {
                      if (e.target.value) {
                        store.changeContainerValid({
                          data: {
                            param: {
                              pickContainerCode,
                            },
                            nextRef: this.locationRef,
                            ref: this.pickContainerCodeRef,
                          },
                        });
                      }
                    }}
                    disabled={pickContainerCodeDisabled}
                    footer={(JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}') || {}).warehouseId === '1' ? '' : (
                      <Button
                        size="small"
                        onClick={(e) => {
                          if (pickContainerCodeDisabled) {
                            store.queryPickingTip({
                              params: { taskCode },
                            })
                              .then(() => {
                                Modal.confirm({
                                  modalBlurInput: true,
                                  content: (
                                    <div>
                                      <div>{t('确认是否换箱？')}</div>

                                      {collectTips && <div>{t('集货提示')}：{collectTips}</div>}
                                      {wellenTips && <div>{t('波次提示')}：{wellenTips}</div>}
                                      {wellenTypeName && <div>{t('波次类型')}：{wellenTypeName}</div>}
                                      <div>
                                        {nationalLineTypeName && (
                                          <span
                                            className={style.nationalLine}
                                          >{nationalLineTypeName}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  ),
                                  onOk: () => new Promise((resolve) => {
                                    store.changePickContainer({
                                      data: {
                                        param: {
                                          pickContainerCode,
                                          taskCode,
                                          pickType,
                                        },
                                        info,
                                        focus: this.pickContainerCodeFocus,
                                        res: resolve,
                                      },
                                    });
                                  }),
                                });
                              });
                          }
                          e.preventDefault();
                        }}
                      >
                        {t('换箱')}
                      </Button>
                    )}
                  >
                    <label>{t('拣货周转箱')}</label>
                  </FocusInput>
                )}
                {/* 图片拣货模式 */}
                {/* 是否开启图片拣货模式 */}
                {openPicturePickingModel && (
                  <img
                    className={style.imgCss} src={info?.imageUrl} onClick={() => {
                      Modal.img({
                        content: <img width="100%" src={info?.imageUrl} />,
                      });
                    }} alt=""
                  />
                )}
                <div style={{ padding: '0 0 5px 15px' }}>{t('波次类型')}: {wellenTypeName}</div>
                <FocusInput
                  data-step="2"
                  data-intro={t('第二步，扫描库位')}
                  disabled={!dataLoading || locationValid || context.showIntroVal}
                  placeholder={t('请扫描库位')}
                  data-bind="locationInput"
                  ref={(node) => {
                    this.locationRef = node;
                  }}
                  className="locationRef"
                  onFocus={() => {
                    store.changeData({
                      data: {
                        lastFocus: 'locationRef',
                      },
                    });
                  }}
                  onPressEnter={(e) => {
                    if (!e.target.value) {
                      return;
                    }
                    store.scanLocationCode({
                      pickType,
                      focus: this.pickContainerCodeFocus,
                    });
                  }}
                  autoFocus={!lastFocus || lastFocus === 'locationRef'}
                  footer={(
                    <Button
                      type="primary"
                      style={{ marginBottom: '5px' }}
                      size="small"
                      onClick={(e) => {
                        e.preventDefault();
                        Modal.confirm({
                          content: t('确认跳过当前库位？'),
                          onOk: () => {
                            const infoParams = {
                              location: isLocation ? `${location}--${sequence}` : location,
                              taskCode,
                              goodsSn,
                              size,
                              pickType,
                              skuCode,
                            };
                            store.skipLocation({
                              infoParams,
                              locationInput,
                            });
                          },
                        });
                      }}
                    >
                      {t('跳过')}
                    </Button>
                  )}
                >
                  <label>{t('库位号')}</label>
                </FocusInput>
                <FocusInput
                  data-step="3"
                  data-intro={t('第三步，扫描商品条码（拣货完成后有提示）')}
                  placeholder={t('请扫描商品条码')}
                  ref={(node) => {
                    this.goodsBarcode = node;
                  }}
                  data-bind="goodsBarcode"
                  disabled={goodsBarcodeValid || context.showIntroVal || !dataLoading}
                  onFocus={() => store.changeData({
                    data: {
                      lastFocus: 'goodsBarcode',
                    },
                  })}
                  onPressEnter={(e) => {
                    if (e.target.value) {
                      if (locationValid) {
                        // >>> 为佛山仓
                        // >>> 条码和周转箱一致
                        // >>> 非图片拣货模式
                        // >>> 执行换箱
                        // TODO 正则去掉首尾空格？
                        if ((JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}') || {}).warehouseId === '1' &&
                          (`${pickContainerCode}`).toLowerCase().trim() === (`${goodsBarcode}`).toLowerCase().trim()
                          && !openPicturePickingModel
                        ) {
                          if (pickContainerCodeDisabled) {
                            store.queryPickingTip({
                              params: { taskCode },
                            })
                              .then(() => {
                                Modal.confirm({
                                  modalBlurInput: true,
                                  content: (
                                    <div>
                                      <div>{t('确认是否换箱？')}</div>

                                      {collectTips && <div>{t('集货提示')}：{collectTips}</div>}
                                      {wellenTips && <div>{t('波次提示')}：{wellenTips}</div>}
                                      {wellenTypeName && <div>{t('波次类型')}：{wellenTypeName}</div>}
                                      <div>
                                        {nationalLineTypeName && (
                                          <span className={style.nationalLine}>
                                            {nationalLineTypeName}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  ),
                                  onOk: () => new Promise((resolve) => {
                                    store.changePickContainer({
                                      data: {
                                        param: {
                                          pickContainerCode,
                                          taskCode,
                                          pickType,
                                        },
                                        info,
                                        focus: this.pickContainerCodeFocus,
                                        res: resolve,
                                      },
                                    });
                                  }),
                                  onCancel: () => {
                                    store.changeData({
                                      data: {
                                        goodsBarcode: '',
                                      },
                                    });
                                    classFocus('goodsBarcode');
                                  },
                                });
                              });
                          }
                          return;
                        }
                        store.changeData({
                          data: {
                            goodsBarcodeValid: true,
                          },
                        });
                        store.postPickData({
                          data: {
                            param: {
                              goodsBarcode,
                              location: locationInput,
                              pickContainerCode,
                              taskCode,
                              pickType,
                            },
                            goBack: this.goBack,
                            pickContainerCodeCheck: this.pickContainerCodeCheck,
                            ref: this.goodsBarcode,
                            successRef: this.locationRef,
                            pickContainerCodeRef: this.pickContainerCodeRef,
                          },
                        });
                      } else {
                        Modal.error({ title: t('请扫描库位') });
                        store.changeData({
                          data: {
                            goodsBarcode: '',
                          },
                        });
                        classFocus('locationRef');
                      }
                    }
                  }}
                  // 图片拣货模式下，商品条码处才展示'换箱'按钮
                  footer={openPicturePickingModel ? (
                    <Button
                      disabled={dataLoading === 0}
                      size="small"
                      onClick={() => {
                        // 跳转到换箱页面
                        store.handleChangeBox({
                          pickType,
                        });
                      }}
                    >
                      {t('换箱')}
                    </Button>
                  ) : null}
                  className="goodsBarcode"
                >
                  <label>{t('商品条码')}</label>
                </FocusInput>
              </Form>
            </View>
            <Footer
              beforeBack={() => {
                store.init();
                const { match: { params: { pickType: pickTypeParam } } } = this.props;
                dispatch(push(`/order-picking/picking/get-task/${pickTypeParam}`));
              }}
            >
              <FooterBtn
                disabled={dataLoading === 0}
                onClick={() => {
                  store.getDetail({
                    params: {
                      // pickContainerCode,
                      taskCode,
                    },
                  });
                }}
              >
                {t('明细')}
              </FooterBtn>
            </Footer>
          </div>
        )}
      </IntroContext.Consumer>
    );
  }
}

PickingPage.propTypes = {
  dataLoading: PropTypes.number,
  pickSeq: PropTypes.number,
  steps: PropTypes.arrayOf(PropTypes.shape()),
  pickingPageCodeObj: PropTypes.shape(),
  match: PropTypes.shape(),
  dispatch: PropTypes.func,
  info: PropTypes.shape(),
  pickContainerCodeDisabled: PropTypes.bool,
  goodsBarcode: PropTypes.string,
  locationInput: PropTypes.string,
  locationValid: PropTypes.bool,
  goodsBarcodeValid: PropTypes.bool,
  timeoutInfo: PropTypes.shape(),
  nextLocations: PropTypes.arrayOf(PropTypes.shape()),
  nationalLineTypeName: PropTypes.string,
  collectTips: PropTypes.string,
  wellenTips: PropTypes.string,
  openPicturePickingModel: PropTypes.bool,
  lastFocus: PropTypes.string,
  wellenTypeName: PropTypes.string,
};

export default PickingPage;
