import React from 'react';
import PropTypes from 'prop-types';
import style from './style.css';

/**
 * 显示目标值和目标进度
 * @param props
 * @returns {*}
 * @constructor
 */
function Battery(props) {
  const {
    haveProfit,
    targetProcess,
  } = props;

  // 显示进度
  const bgStyle = { background: `linear-gradient(to right, #BDDAFF ${targetProcess}%, #FFFFFF ${targetProcess}%)` };

  return (
    <div className={style.battery} style={bgStyle}>
      <span
        className={style.haveProfit}
        style={{ color: haveProfit < 0 ? '#F85555' : '#013B86' }}
      >
        &nbsp;&yen;{haveProfit.toFixed(2)}
      </span>
      <span className={style.targetProcess}>{targetProcess}%</span>
    </div>
  );
}
Battery.propTypes = {
  haveProfit: PropTypes.number,
  targetProcess: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

Battery.defaultProps = {
  haveProfit: 0,
  targetProcess: 0,
};

export default Battery;
