import { sendPostRequest } from '../../../lib/public-request';

export const pdaPickGoodsNum = (param) => sendPostRequest({
  url: '/pda/pda_pick_goods_num',
  param,
}, process.env.WOS_URI);

export const pdaTakePickTask = (param) => sendPostRequest({
  url: '/pda/pda_take_pick_task',
  param,
}, process.env.WOS_URI);

export const pdaShortPick = (param) => sendPostRequest({
  url: '/pda/pda_short_pick',
  param,
}, process.env.WOS_URI);

export const pdaPostPickData = (param) => sendPostRequest({
  url: '/pda/pda_post_pick_data',
  param,
}, process.env.WOS_URI);

export const pdaChangePickContainer = (param) => sendPostRequest({
  url: '/pda/pda_change_pick_container',
  param,
}, process.env.WOS_URI);

export const pdaValidPickContainer = (param) => sendPostRequest({
  url: '/pda/pda_validate_pick_container',
  param,
}, process.env.WOS_URI);
// 查询当前用户排名
export const pdaQueryRankInfo = (param) => sendPostRequest({
  baseUrl: process.env.WKB,
  url: '/rank/query',
  param,
});
export const getDetailApi = (param) => sendPostRequest({
  url: '/pda/pda_pick_detail',
  param,
}, process.env.WOS_URI);
/**
 * 校验任务是否已完结
 * @param param
 */
export const pdaValidateTaskApi = (param) => sendPostRequest({
  url: '/pda/pda_validate_task',
  param,
}, process.env.WOS_URI);
/**
 * 跳过库位
 * @param param
 */
export const skipLocationApi = (param) => sendPostRequest({
  url: '/pda/pda_take_skip_location',
  param,
}, process.env.WOS_URI);

// 修改本日收益目标
export const modifyTargetApi = (param) => sendPostRequest({
  url: '/profit_daily/modify_target',
  param,
}, process.env.WMS_INTERNAL_FRONT);

// 获取排名效率信息
export const getRankAndRatioInfoApi = (param) => sendPostRequest({
  url: '/user_rank_and_ratio/query',
  param,
}, process.env.WKB);

// 获取集货分流提示
export const queryPickingTipApi = (param) => sendPostRequest({
  url: '/pda/query_picking_tip',
  param,
}, process.env.WOS_URI);

// 拣货任务明细推荐请求
export const taskDetailRecommend = (param) => sendPostRequest({
  url: '/pda/task_detail_recommend',
  param,
});

// 获取系统配置
export const queryByCodeApi = ({ code, ...param }) => sendPostRequest({
  url: `/config/queryByCode?code=${code}`,
  param,
}, process.env.BASE_URI_WMD);

// 埋点接口
// export const trackAddApi = (param) => sendPostRequest({
//   url: '/track/add',
//   param,
// }, process.env.WOS_URI);

/**
 * 查询是否满足换箱条件 以及 旧箱信息
 */
export const changeOldContainerConfirmAPI = (param) => sendPostRequest({
  url: '/pda/picture_pda_change_old_container_confirm',
  param,
}, process.env.WOS_URI);

/**
 * 新箱换箱
 * @param param
 * @returns {*}
 */
export const confirmChangeNewBoxAPI = (param) => sendPostRequest({
  url: '/pda/picture_pda_change_container_confirm',
  param,
}, process.env.WOS_URI);
