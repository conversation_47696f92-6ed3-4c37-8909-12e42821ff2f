import React from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import style from './style.css';
import CountDown from '../../common/count-down';
import { getLang } from '../../js';

const isChinese = getLang() === 'zh';

function Info(props) {
  const {
    info: data,
    timeoutInfo,
    onEnd,
    onSoon,
    nationalLineTypeName,
    match: { params: { pickType } },
    openPicturePickingModel,
  } = props;
  const displayCountDown = (datas) => {
    if (!datas) {
      return null;
    }
    const {
      timeoutSeconds,
      soonOvertimeSeconds,
      overtimeAgainSeconds,
    } = datas;
    return (-timeoutSeconds > 24 * 60 * 60) ? (
      <div
        style={{
          backgroundColor: '#d8e2f7',
        }}
      >
        {t('该任务时长超过24小时')}
      </div>
    ) : (
      <CountDown
        countLabelText={`${t('该任务还剩')}:`}
        timeoutSeconds={timeoutSeconds}
        soonOvertimeSeconds={soonOvertimeSeconds}
        onEnd={() => onEnd(timeoutSeconds, overtimeAgainSeconds)}
        onSoon={() => onSoon()}
      />
    );
  };

  // 这次改造只改正常拣货
  const getColor = (info) => {
    let color = '';
    switch (pickType) {
      case '1':
        color = isChinese ? info.colorZh : info.colorEn;
        break;
      default:
        color = info.color;
        break;
    }
    return color;
  };

  return (
    <div>
      {/* 是否开启图片拣货模式 */}
      {!openPicturePickingModel && (
        <Form>
          <div className={style.firstItem}>
            <span>{data.taskCode}</span>
            {nationalLineTypeName &&
              <span className={style.nationalLine}>{nationalLineTypeName}</span>}
            <div className={style.lineHeight}>
              {t('已拣/总件数')}:
              <span className={style.weight}>
                <span style={{ color: '#ff9636' }}>{data.hadPickNum}</span>
                /{data.totalGoodsNum}
              </span>
            </div>
          </div>
        </Form>
      )}
      {/* 是否开启图片拣货模式 */}
      {!openPicturePickingModel && (
        <div
          className={style.tipsBox}
          style={{ flexDirection: 'row' }}
        >
          {displayCountDown(timeoutInfo)}
        </div>
      )}
      <div className={style.mainInfo}>
        <div className={style.boxFloat}>
          <div className={style.floatLeft}>{t('库位号')}</div>
          <div className={style.floatRight}>
            {
              Number(data.taskMode) !== 2 ? ( // 任务模式,0-单任务，1-多任务，2-合并任务
                <>
                  <span
                    style={
                      {
                        fontSize: 20,
                        fontWeight: 'normal',
                      }
                    }
                    dangerouslySetInnerHTML={{ __html: data.location?.replace(/(.{6})(.{0,7})(.*)/, '$1<b style="font-size: 25px ">$2</b>$3') }}
                  />
                  <br />
                  <span
                    style={{
                      float: 'right', color: 'red', fontWeight: 700, lineHeight: 1,
                    }}
                  >
                    {data.isLocation ? `-${data.sequence}` : ''}
                  </span>
                </>
              ) : (
                <>
                  <span
                    style={
                      {
                        fontSize: 20,
                        fontWeight: 'normal',
                      }
                    }
                    dangerouslySetInnerHTML={{ __html: data.location?.replace(/(.{6})(.{0,7})(.*)/, '$1<b style="font-size: 25px ">$2</b>$3') }}
                  />
                  <span style={{ color: 'red', fontSize: 20, fontWeight: 700 }}>
                    {data.isLocation ? `--${data.sequence}` : ''}
                  </span>
                </>
              )
            }
          </div>
        </div>
        <div className={style.mainItem} style={{ lineHeight: '32px', height: 32 }}>
          <span>{t('SKC')}</span>
          {/* 是否开启图片拣货模式 */}
          {openPicturePickingModel && (
            <div style={{ fontSize: 25, fontWeight: 'bold', color: '#FF0000' }}>
              {data.size}
            </div>
          )}
          <div style={{ color: 'red' }}>
            <span>{data.goodsSn?.slice(0, -4)}</span>
            <span style={{ fontSize: 25, fontWeight: 'bold' }}>{data.goodsSn?.slice(-4)}</span>
          </div>
        </div>
        {/* 是否开启图片拣货模式 */}
        {!openPicturePickingModel && (
          <div className={classnames(style.mainItem, style.sizeAndColorItem)}>
            <div
              style={{
                fontSize: 25,
                fontWeight: 'bold',
                color: '#FF0000',
                marginRight: 50,
              }}
            >
              {getColor(data)}
            </div>
            <div style={{ fontSize: 25, fontWeight: 'bold', color: '#FF0000' }}>
              {data.size}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

Info.propTypes = {
  info: PropTypes.shape(),
  timeoutInfo: PropTypes.shape(),
  onEnd: PropTypes.func,
  onSoon: PropTypes.func,
  nationalLineTypeName: PropTypes.string,
  match: PropTypes.shape(),
  openPicturePickingModel: PropTypes.bool,
};

export default Info;
