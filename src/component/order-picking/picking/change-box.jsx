import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import {
  Header,
  Footer,
  FocusInput,
} from 'common';
import { Form } from 'react-weui/build/packages';
import store from './reducers';
import styles from './style.css';
import { OLD_BOX } from './constants';
import { classFocus } from '../../../lib/util';

function ChangeBox(props) {
  const {
    changeBoxFlag,
    oldContainerCode,
    newContainerCode,
    dataLoading,
    lastFocus,
    boxInfo: {
      pickContainerCode,
      count,
      goodsInfo,
      wellenCode,
      wellenTips,
      collectTips,
      nationalLineTypeStr,
      totalNum = '',
      waitePickNum = '',
    },
    match: { params: { pickType } },
    wellenTypeName,
  } = props;
  return (
    <>
      <Header
        homeIcon={false}
        title={changeBoxFlag === OLD_BOX ? t('旧箱确认') : t('新箱确认')}
      />
      <Form>
        <FocusInput
          autoFocus
          value={changeBoxFlag === OLD_BOX ? oldContainerCode : newContainerCode}
          className="boxCode"
          placeholder={changeBoxFlag === OLD_BOX ? t('请录入旧拣货箱') : t('请录入新拣货箱')}
          disabled={!dataLoading}
          onChange={(e) => {
            const { value } = e.target;
            store.changeData({
              data: {
                [changeBoxFlag === OLD_BOX ? 'oldContainerCode' : 'newContainerCode']: value?.trim(),
              },
            });
          }}
          onPressEnter={(e) => {
            // 空值不触发请求【是否在FocusInput统一处理】
            const { value } = e.target;
            if (!value) {
              return;
            }
            store.confirmBoxCode({
              boxCode: value.trim(),
              isNewBox: changeBoxFlag !== OLD_BOX,
              pickType,
            });
          }}
        >
          <label>{changeBoxFlag === OLD_BOX ? t('旧箱号') : t('新箱号')}</label>
        </FocusInput>
      </Form>
      <Form className={styles.showChangeBoxInfoForm}>
        {changeBoxFlag === OLD_BOX ? (
          <>
            <div>
              <span className={styles.changeBoxInfoLabel}>{`${t('当前箱号')}:`}</span>
              <span>{pickContainerCode?.slice(0, -4)}</span>
              <span className={styles.redText}>{pickContainerCode?.slice(-4)}</span>
            </div>
            <div>
              <span className={styles.changeBoxInfoLabel}>{`${t('箱内件数')}:`}</span>
              <span className={styles.redText}>{count}</span><span>{` ${t('件')}`}</span>
            </div>
            <div className={styles.changeBoxInfoLabel}>{`${t('该箱最后一件商品')}:`}</div>
            <div>
              <span className={styles.changeBoxInfoLabel}>
                <span>{goodsInfo?.goodsSn?.slice(0, -4)}</span>
                <span className={styles.redText}>{goodsInfo?.goodsSn?.slice(-4)}</span>
              </span>
              <span className={styles.redText}>{goodsInfo?.size}</span>
            </div>
            <div>
              <span className={styles.changeBoxInfoLabel}>{`${t('波次号')}:`}</span>
              <span>{wellenCode}</span>
            </div>
            {/* 集货提示与分流提示存在值才展示 */}
            {collectTips && (
            <div>
              <span className={styles.changeBoxInfoLabel}>{`${t('集货提示')}:`}</span>
              <span className={styles.redText}>{collectTips}</span>
            </div>
            )}
            {wellenTips && (
            <div>
              <span className={styles.changeBoxInfoLabel}>{`${t('波次提示')}:`}</span>
              <span className={styles.redText}>{wellenTips}</span>
            </div>
            )}
            {wellenTypeName && (
              <div>
                <span className={styles.changeBoxInfoLabel}>{`${t('波次类型')}:`}</span>
                <span className={styles.redText}>{wellenTypeName}</span>
              </div>
            )}
            <div className={styles.nationalText}>{nationalLineTypeStr}</div>
          </>
        ) : (
        // 此处是新箱确认页面展示信息
          <>
            <div>
              <span className={styles.changeBoxInfoLabel}>{`${t('波次号')}:`}</span>
              <span>{wellenCode}</span>
            </div>
            <div>
              <span className={styles.changeBoxInfoLabel}>{`${t('待拣件数')}/${t('总件数')}`}</span>
              <span>
                <span className={styles.redText}>{waitePickNum}</span>/
                {totalNum}
              </span>
              <div className={styles.nationalText}>{nationalLineTypeStr}</div>
            </div>
          </>
        )}
      </Form>
      <Footer
        beforeBack={() => {
          // 若是旧箱确认 - 返回拣货页面
          if (changeBoxFlag === OLD_BOX) {
            store.resetChangeBoxInfo();
            classFocus(lastFocus || 'locationRef');
          } else {
            store.changeData({
              data: {
                changeBoxFlag: OLD_BOX,
                // 清除旧箱号
                oldContainerCode: '',
              },
            });
            classFocus('boxCode');
          }
          // 若是新箱确认 - 返回旧箱确认页面
        }}
      />
    </>
  );
}

ChangeBox.propTypes = {
  dataLoading: PropTypes.number,
  changeBoxFlag: PropTypes.number,
  oldContainerCode: PropTypes.string,
  newContainerCode: PropTypes.string,
  boxInfo: PropTypes.shape(),
  lastFocus: PropTypes.string,
  match: PropTypes.shape(),
  wellenTypeName: PropTypes.string,
};

export default ChangeBox;
