import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import { Form } from 'react-weui/build/packages';
import LoadMore from 'react-weui/build/packages/components/loadmore';
import UserIntro from 'common/user-intro';
import { push } from 'react-router-redux';
import { LOCAL_PICKING_PAGE_CODE_OBJ } from 'lib/storage';
import Footer from '../../common/footer';
import IntroContext from '../../user-intro-page/IntroContext';
import FocusInput from '../../common/focus-input';
import store from './reducers';
import navStore from '../../nav/reducers';
import RowInfo from '../../common/row-info';
import Header from '../../common/header';
import { classFocus } from '../../../lib/util';

class GetTask extends React.Component {
  componentDidMount() {
    const { match: { params: { pickType } } } = this.props;
    store.getPickNum({
      data: {
        pickType,
      },
    });
    if (document.URL.indexOf('order-picking/picking/get-task') !== -1) {
      localStorage.setItem(LOCAL_PICKING_PAGE_CODE_OBJ, '{}');
    }
    navStore.changeData({ data: { pageStore: store } });
  }

  componentDidUpdate() {
    navStore.changeData({ data: { pageStore: store } });
  }

  render() {
    const {
      dataLoading,
      dispatch,
      pickContainerCode,
      pickCount,
      pickContainerCodeValid,
      pickTaskNums,
      match: { params: { pickType } },
      waitExceptionTaskNums,
      pickingBox,
      pickSeq,
      steps,
      userRankAndRatio,
      isShowFlag,
    } = this.props;
    const { rank, ratio } = userRankAndRatio;
    return (
      <IntroContext.Consumer>
        {(context) => (
          <div>
            {
                dataLoading === 0 ? <LoadMore loading={dataLoading === 0} /> : (
                  <div>
                    <Header title={pickType === '1' ? t('正常拣货') : t('异常拣货')} homeIcon={pickType !== '1'}>
                      {
                  pickType === '1' && (
                    <span
                      style={{ position: 'absolute', left: -290, padding: '0 10px' }}
                      onClick={() => {
                        store.changeData({ data: { showSettingModal: true, oldPickSeq: pickSeq } });
                      }}
                    >
                      <Icon style={{ fontSize: 20 }} name="setting" />
                    </span>
                  )
                }
                    </Header>
                    <UserIntro
                      showIntro={context.showIntroVal}
                      steps={steps}
                      endHandle={() => {
                        navStore.changeData({ data: { showIntro: false } });
                        dispatch(push(`/order-picking/picking/picking-page/${pickType}`));
                        navStore.changeData({ data: { showIntro: true } });
                      }}
                    />
                    <Form>
                      {/* 正常拣货隐藏 */}
                      {pickType !== '1' && (
                      <RowInfo
                        key={t('待领任务数')}
                        extraStyle={{
                          borderBottom: 'none',
                        }}
                        label={t('待领任务数')}
                        content={pickType === '1' ? pickTaskNums : waitExceptionTaskNums}
                        type="info"
                      />
                      )}
                      <RowInfo
                        key={t('已拣总件数')}
                        extraStyle={{
                          borderBottom: 'none',
                        }}
                        label={t('已拣总件数')}
                        content={pickCount}
                        type="warn"
                      />
                      {isShowFlag && (
                        <div>
                          <RowInfo
                            key={t('当前用户排名')}
                            extraStyle={{
                              borderBottom: 'none',
                            }}
                            label={t('当前用户排名')}
                            content={rank || ''}
                            type="sky"
                          />
                          <RowInfo
                            key={t('当前用户效率')}
                            extraStyle={{
                              borderBottom: 'none',
                            }}
                            label={t('当前用户效率')}
                            content={`${ratio}${t('件/小时')}` || ''}
                            type="sky"
                          />
                        </div>
                      )}
                      <RowInfo
                        key={t('拣货中周转箱')}
                        extraStyle={{
                          borderBottom: 'none',
                        }}
                        label={t('拣货中周转箱')}
                        content={(
                          <div
                            style={{
                              color: '#0059ce', cursor: 'pointer', position: 'relative', left: 5,
                            }}
                          >
                            {
                              pickingBox && (
                                <div
                                  onClick={() => {
                                    store.changeData({
                                      data: {
                                        pickContainerCode: pickingBox,
                                      },
                                    });
                                    store.takePickTask({
                                      data: {
                                        param: {
                                          pickContainerCode: pickingBox,
                                          pickType,
                                        },
                                        ref: this.pickContainerCode,
                                      },
                                    });
                                    classFocus('pickContainerCodeInput');
                                  }}
                                >
                                  <span>{pickingBox}</span>
                                  <Icon
                                    name="arr-right"
                                  />
                                </div>
                              )
                            }
                          </div>
                        )}
                        type="info"
                      />
                    </Form>
                  </div>
                )
              }
            <Form
              style={{ marginTop: '10px' }}
            >
              <FocusInput
                data-step="1"
                data-intro={pickType === '1' ? t('第一步，扫描拣货周转箱') : t('第一步，扫描异常补拣周转箱')}
                autoFocus
                value={pickContainerCode}
                className="pickContainerCodeInput"
                placeholder={pickType === '1' ? t('请扫描拣货周转箱') : t('请扫描异常补拣周转箱')}
                ref={(node) => {
                  this.pickContainerCode = node;
                }}
                onChange={(e) => {
                  const { value } = e.target;
                  store.changeData({
                    data: {
                      pickContainerCode: value.trim(),
                    },
                  });
                }}
                onPressEnter={() => {
                  if (pickContainerCode) {
                    store.takePickTask({
                      data: {
                        param: {
                          pickContainerCode, pickType,
                        },
                        ref: this.pickContainerCode,
                      },
                    });
                  }
                }}
                onBlur={() => {
                  if (pickContainerCode) {
                    store.takePickTask({
                      data: {
                        param: {
                          pickContainerCode, pickType,
                        },
                        ref: this.pickContainerCode,
                      },
                    });
                  }
                }}
                disabled={pickContainerCodeValid || context.showIntroVal}
              >
                <label>{t('拣货周转箱')}</label>
              </FocusInput>
            </Form>
            <Footer
              beforeBack={(back) => {
                store.init();
                back();
              }}
            />
          </div>
        )}
      </IntroContext.Consumer>
    );
  }
}

GetTask.propTypes = {
  dataLoading: PropTypes.number,
  pickSeq: PropTypes.number,
  pickingBox: PropTypes.string,
  steps: PropTypes.arrayOf(PropTypes.shape()),
  userRankAndRatio: PropTypes.shape(),
  isShowFlag: PropTypes.bool,
  match: PropTypes.shape(),
  dispatch: PropTypes.func,
  pickContainerCode: PropTypes.string,
  pickCount: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  pickContainerCodeValid: PropTypes.bool,
  pickTaskNums: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  waitExceptionTaskNums: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
};

export default GetTask;
