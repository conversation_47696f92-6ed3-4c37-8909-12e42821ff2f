import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { i18n } from '@shein-bbl/react';
import UserIntro from 'common/user-intro';
import { SESSION_PICK_SEQ_VAL } from 'lib/storage';
import GetTask from './get-task';
import PickingPage from './picking-page';
import DetailPage from './detail-page';
import ChangeBox from './change-box';
// import { DragCircle } from '../../common';
import navStore from '../../nav/reducers';
import store from './reducers';
import { classFocus } from '../../../lib/util';
// import ModalTarget from './modal-target';
import SettingModal from './setting-modal';

class Container extends Component {
  componentDidMount() {
    // store.initCodeApi();
    store.getDegradeConfig();
    // 从sesstion获取正反序默认值，不退出系统则一直保存在前端;
    store.changeData({
      data: {
        pickSeq: sessionStorage.getItem(SESSION_PICK_SEQ_VAL) === '1' ? 1 : 0,
      },
    });
  }

  render() {
    const {
      dispatch,
      match: { params: { status } },
      info,
      showDetail,
      detailList,
      steps,
      showChangeBox,
      lastFocus,
    } = this.props;
    let children;
    switch (status) {
      case 'get-task':
        children = (<GetTask {...this.props} />);
        break;
      case 'picking-page':
        children = (<PickingPage {...this.props} />);
        break;
      default:
        break;
    }
    if (showDetail) {
      children = (
        <DetailPage
          data={detailList}
          dispatch={dispatch}
          lastFocus={lastFocus}
        />
      );
    }
    if (showChangeBox) {
      children = (
        <ChangeBox
          {...this.props}
        />
      );
    }
    return (
      <div style={{ marginBottom: '56px' }}>
        <UserIntro
          steps={steps}
        />
        {children}
        {/* <DragCircle */}
        {/*  onClick={() => { */}
        {/*    navStore.changeData({ data: { showUploadError: true } }); */}
        {/* eslint-disable-next-line max-len */}
        {/*    navStore.changeLimit({ data: { location: info && info.location ? info.location : '' } }); */}
        {/*    classFocus('location'); */}
        {/*  }} */}
        {/* /> */}
        {/* <ModalTarget {...this.props} /> */}
        <SettingModal {...this.props} />
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  info: PropTypes.shape(),
  showDetail: PropTypes.bool,
  match: PropTypes.shape(),
  detailList: PropTypes.arrayOf(PropTypes.arrayOf(PropTypes.shape())),
  steps: PropTypes.arrayOf(PropTypes.shape()),
  showChangeBox: PropTypes.bool,
  lastFocus: PropTypes.string,
};

const mapStateToProps = (state) => state['order-picking/picking'];
export default connect(mapStateToProps)(i18n(Container));
