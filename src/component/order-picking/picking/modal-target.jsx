import React from 'react';
import PropTypes from 'prop-types';
import { Dialog } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import store from './reducers';
import style from './style.css';
import { isInt, classFocus, isaNumberInRange } from '../../../lib/util';
import { modal } from '../../common';

function ModalTarget(props) {
  const {
    targetModalVisible,
    yesterdayProfit,
    profitTarget,
    profitId,
  } = props;

  const getMsg = (num) => {
    if (!num) {
      return t('目标值不能为空');
    }
    if (!isaNumberInRange(num, 0, 9999)) {
      return t('目标值范围为1-9999');
    }
    return '';
  };

  return (
    <Dialog
      title={`${t('提示')}`}
      show={targetModalVisible}
      buttons={[{
        label: t('确定'),
        type: 'primary',
        onClick: () => {
          const msg = getMsg(profitTarget);
          if (msg) {
            modal.error({
              title: msg,
              onOk: () => {
                store.changeData({ data: { profitTarget: '' } });
                classFocus('yesterdayProfitInputBox');
              },
            });
            return;
          }
          if (profitTarget && isInt(profitTarget)) {
            // 设置目标值
            store.modifyTarget({
              params: {
                details: [{
                  profitTarget,
                  operateType: 1,
                }],
                profitId,
              },
            });
            store.changeData({ data: { targetModalVisible: false } });
            classFocus('pickContainerCodeInput');
          }
        },
      }]}
    >
      <div>{t('你昨天干了')}{yesterdayProfit}{t('元')}</div>
      <div style={{ marginTop: 5 }}>
        <span>{t('今天想赚')}</span>
        <input
          className={[style.inputBox, 'yesterdayProfitInputBox'].join(' ')}
          type="text"
          value={profitTarget}
          onInput={(e) => {
            const { value } = e.target;
            if (parseInt(value, 10)) {
              store.changeData({ data: { profitTarget: parseInt(value, 10) } });
            } else {
              store.changeData({ data: { profitTarget: '' } });
            }
          }}
           // 加上onChange方法解决控制台的报错
          onChange={() => {}}
        />
        <span>{t('元')}</span>
      </div>
    </Dialog>
  );
}
ModalTarget.propTypes = {
  targetModalVisible: PropTypes.bool,
  yesterdayProfit: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  profitTarget: PropTypes.string,
  profitId: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
};

export default ModalTarget;
