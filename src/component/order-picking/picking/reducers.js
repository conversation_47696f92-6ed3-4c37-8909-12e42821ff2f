import React from 'react';
import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { push } from 'react-router-redux';
import { select } from 'redux-saga/effects';
import { t } from '@shein-bbl/react';
import { classFocus, apolloFormatObj, getWarehouseId } from 'lib/util';
import { LOCAL_USER, LOCAL_PICKING_PAGE_CODE_OBJ, LOCAL_WAREHOUSE } from 'lib/storage';
import FocusInput from '../../common/focus-input';
import Modal from '../../common/modal';
import error from '../../../source/audio/delete.mp3';
import { getApolloConfigAPI } from '../../../server/basic/common';
import {
  pdaTakePickTask,
  pdaShortPick,
  pdaPostPickData,
  pdaChangePickContainer,
  pdaPickGoodsNum,
  pdaValidPickContainer,
  getDetail<PERSON><PERSON>,
  pdaValidate<PERSON>ask<PERSON><PERSON>,
  skipLocation<PERSON><PERSON>,
  modifyTarget<PERSON><PERSON>,
  getRankAndRatioInfoApi,
  queryPickingTipApi,
  taskDetailRecommend,
  changeOldContainerConfirmAPI, confirmChangeNewBoxAPI,
} from './server';
import { NEW_BOX, OLD_BOX } from './constants';
import style from './style.css';
import tipAudioPlay from './tip-player';
import message from '../../common/message';

// TODO 正则去掉首尾空格？
export const isStrEqual = (str1, str2) => str1.toString()
  .toLowerCase()
  .trim() === str2.toString()
  .toLowerCase()
  .trim();

const baseState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  info: {
    goodsSn: '',
    hadPickNum: '',
    location: '',
    pickContainerCode: '',
    pickNum: '',
    size: '',
    skuCode: '',
    taskCode: '',
    taskDetailId: '',
    totalGoodsNum: '',
    outSubWarehouseName: '',
  },
  timeoutInfo: null,
  nationalLineTypeName: '',
  collectTips: '', // 集货提示
  wellenTips: '', // 波次提示
  wellenTypeName: '', // 波次类型
  steps: [
    {
      element: '.pickContainerCodeInput',
      intro: t('第一步,扫描周转箱,领取拣货任务'),
    },
    {
      element: '.locationRef',
      intro: t('第二步,扫描库位'),
    },
    {
      element: '.goodsBarcode',
      intro: t('第三步,扫描商品条码'),
    },
  ],
  degradeScanUpgrade: false, // 降级开关
};

const otherState = {
  locationValid: false,
  goodsBarcodeValid: false,
  pickContainerCodeValid: false,
  pickCount: '',
  pickTaskNums: '',
  pickContainerCode: '',
  locationInput: '',
  goodsBarcode: '',
  pickContainerCodeDisabled: true,
  isLocation: false,
  sequence: '',
  rankVal: '', // 当前用户排名
  total: '',
  waitExceptionTaskNums: '', // 异常待领取任务数
  pickingBox: '',
  nextLocations: [],
  detailList: [[], []],
  showDetail: false,
  targetModalVisible: false, // 设置目标弹窗
  yesterdayProfit: '', // 昨天收益
  // trackSwitch: true, // 扫库位号埋点开关
  profitId: '', // 目标id
  haveProfit: 0, // 今日累计收益
  targetProcess: '', // 目标进度
  rankStr: '', // 排名信息
  userRank: '', // 用户排名
  totalRank: '', // 总人数
  mostProfitUser: '', // 收益最多的人
  pickSeq: 0, // 0正序, 1反序
  oldPickSeq: 0, // 用于存储打开弹窗时的值，方便进行判断是否更改和取消更改
  showSettingModal: false,
  takePickTaskParam: {}, // 用于改变顺序时，重新领取任务
  steps: [],
  pickContainerCodeCheck: '', // 核对箱号输入框
  operator: (JSON.parse(localStorage.getItem(LOCAL_USER)) || {}).username || '',
  pickingPageCodeObj: {},
  isShowFlag: false, // 是否展示排名和效率
  userRankAndRatio: {
    rank: '', // 排名
    ratio: '', // 效率
  }, // 用户排名和效率信息
  userName: (JSON.parse(localStorage.getItem(LOCAL_USER)) || {}).username || '',
  openPicturePickingModel: false, // 左侧栏开启图片拣货模式
  showChangeBox: false, // 是否展示换箱页面
  changeBoxFlag: OLD_BOX, // 默认为旧箱确认
  oldContainerCode: '', // 旧箱号
  newContainerCode: '', // 新箱号
  boxInfo: {}, // 旧箱号相关信息
  lastFocus: '', // 记录上次聚焦的位置
};

export const defaultState = assign({}, baseState, otherState);

const audio = new Audio(error);
audio.load();
// 新增正反序接口字段
const addParam = (param, pickSeq) => {
  // 非正常拣货页面，则不新增参数
  if (!window.location.hash.endsWith('/1')) {
    return { ...param, pickSeq: 0 };
  }
  return { ...param, pickSeq };
};

const pushStateFn = () => {
  if (document.URL.indexOf('picking-page') !== -1) {
    // 没有校验箱号 禁止回退
    window.history.pushState(null, null, document.URL);
    window.addEventListener('popstate', (e) => {
      // 微前端劫持触发的不执行，防止死循环
      if (e?.singleSpa) {
        return;
      }
      // 优化：防止影响其它页面返回
      if (document.URL.indexOf('picking-page') !== -1) {
        window.history.pushState(null, null, document.URL);
      }
    });
  }
};

export default {
  state: defaultState,
  init: () => defaultState,
  // 初始当前页面数据
  $resetPageStore(draft) {
    // eslint-disable-next-line max-len
    assign(draft, defaultState, {
      headerTitle: draft.headerTitle,
      pickSeq: draft.pickSeq,
      degradeScanUpgrade: draft.degradeScanUpgrade,
    });
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },

  // 重置换箱相关数据
  resetChangeBoxInfo(draft) {
    assign(draft, {
      showChangeBox: false,
      changeBoxFlag: OLD_BOX,
      oldContainerCode: '',
      newContainerCode: '',
      boxInfo: {},
      lastFocus: '',
    });
  },
  $takePickTask(draft) {
    assign(draft, {
      pickContainerCodeValid: true,
    });
  },
  $changeContainerValid(draft) {
    assign(draft, {
      pickContainerCodeDisabled: true,
    });
  },
  changeCurrent(draft, action) {
    assign(draft, { info: assign(draft.info, action.data) });
  },
  * resetPageStore(action, ctx, put) {
    const pickType = window.location.hash.endsWith('/1') ? '1' : '2';
    yield put(push(`/order-picking/picking/get-task/${pickType}`));
  },
  /**
   * PDA拣货领取任务
   * @param action
   * @param ctx
   * @param put
   * @returns {IterableIterator<PutEffect<Action> | *>}
   */
  * takePickTask(action, ctx, put) {
    const { pickSeq } = yield select((state) => state['order-picking/picking']);
    const result = yield pdaTakePickTask(addParam(action.data.param, pickSeq));
    if (result.code === '0') {
      yield ctx.changeData({
        data: {
          info: assign({}, result.info, { location: result.info && result.info.location ? result.info.location.trim() : '' }),
          timeoutInfo: result.info.timeoutInfo,
          nextLocations: result.info.nextLocations,
          nationalLineTypeName: result.info.nationalLineTypeName || '',
          collectTips: result.info.collectTips || '',
          wellenTips: result.info.wellenTips || '',
          wellenTypeName: result.info.wellenTypeName || '',
          takePickTaskParam: { ...action.data },
        },
      });
      const { changeAreaInfo, taskStatus } = result.info;
      // changeAreaInfo不为空代表已经进行换区
      if (changeAreaInfo) {
        const {
          area,
          floor,
          lastArea,
          lastFloor,
        } = changeAreaInfo;
        // 换区提示框
        const status = yield new Promise((r) => Modal.info({
          content: (
            <div>
              {t('本任务将由上一个任务的')}
              <span>{lastFloor}</span>
              {t('层')}
              <span style={{ color: 'red' }}>{lastArea}</span>
              {t('区')}{t('切换为')}
              <span>{floor}</span>
              {t('层')}
              <span style={{ color: 'red', fontSize: 18 }}>{area}</span>
              {t('区')},{t('请注意')}！
            </div>
          ),
          onOk: () => r('ok'),
        }));
        // 点击确认进行页面调整
        if (status === 'ok') {
          yield put(push(`/order-picking/picking/picking-page/${action.data.param.pickType}`));
        }
      } else {
        yield put(push(`/order-picking/picking/picking-page/${action.data.param.pickType}`));
      }
      // OFC-13792 拣货前取消商品无需拣货
      if (taskStatus === 1) {
        const flag = yield new Promise((r) => Modal.info({
          content: t('任务已取消，请扫描周转箱重新领取任务'),
          onOk: () => r('ok'),
        }));
        if (flag === 'ok') {
          yield ctx.resetPageStore();
          classFocus('pickContainerCodeInput');
        }
        return;
      } else if (taskStatus === 2) {
        yield ctx.showEndModalNew();
        return;
      }
      if (result.info.isFinishedTask && !action.data.noTipModal) {
        const status = yield new Promise((r) => (
          Modal.confirm({
            content: t('您存在未完成的任务'),
            buttons: [{
              type: 'primary',
              label: t('确定'),
              onClick: () => (r('ok')),
            }],
          })
        ));
        if (status === 'ok') {
          classFocus('pickContainerCodeInput');
        }
      }
    } else {
      const status = yield new Promise((r) => Modal.error({
        title: result.msg,
        onOk: () => r('ok'),
      }));
      if (status === 'ok' && action.data.ref) {
        classFocus(`${action.data.ref.props.className}`);
      }
    }
    yield ctx.changeData({
      data: {
        pickContainerCodeValid: false,
        pickContainerCode: '',
      },
    });
  },
  * showEndModalNew(action, ctx) {
    const {
      nationalLineTypeName,
      collectTips,
      wellenTips,
      wellenTypeName,
    } = yield select((state) => state['order-picking/picking']);
    const status = yield new Promise((r) => Modal.confirm({
      content: (
        <div>
          <div>{t('任务拣货完成')}!</div>
          {collectTips && <div>{t('集货提示')}：{collectTips}</div>}
          {wellenTips && <div>{t('波次提示')}：{wellenTips}</div>}
          {wellenTypeName && <div>{t('波次类型')}：{wellenTypeName}</div>}
          <div>
            {nationalLineTypeName &&
              <span className={style.nationalLine}>{nationalLineTypeName}</span>}
          </div>
        </div>
      ),
      buttons: [{
        type: 'primary',
        label: t('确定'),
        onClick: () => (r('ok')),
      }],
    }));
    if (status === 'ok') {
      yield ctx.resetPageStore();
      yield ctx.changeData({
        data: {
          pickContainerCodeValid: false,
          pickContainerCode: '',
        },
      });
      classFocus('pickContainerCodeInput');
    }
  },
  * handleBackFlag(action, ctx) {
    // 1 表示当前任务拣货完成需要跳转领取任务页面
    if (+action.data.info.backFlag === 1) {
      const {
        nationalLineTypeName,
        collectTips,
        wellenTips,
        wellenTypeName,
      } = yield select((state) => state['order-picking/picking']);
      if (+action.data.info.popUpWindowFlag === 2) {
        localStorage.setItem(LOCAL_PICKING_PAGE_CODE_OBJ, JSON.stringify({
          pickContainerCode: action.data.param.pickContainerCode,
          taskCode: action.data.param.taskCode,
        }));
        pushStateFn();
      }
      const status = yield new Promise((r) => Modal.confirm({
        content: (
          <div>
            <div>{t('任务拣货完成')}!</div>
            {collectTips && <div>{t('集货提示')}：{collectTips}</div>}
            {wellenTips && <div>{t('波次提示')}：{wellenTips}</div>}
            {wellenTypeName && <div>{t('波次类型')}：{wellenTypeName}</div>}
            <div>
              {nationalLineTypeName &&
                <span className={style.nationalLine}>{nationalLineTypeName}</span>}
            </div>
            {action.data.param.pickContainerCode &&
              <div>{t('拣货周转箱')}：{action.data.param.pickContainerCode}</div>}
          </div>
        ),
        buttons: [+action.data.info.popUpWindowFlag === 2 ? (
          {
            type: 'primary',
            noClose: true,
            label: (
              <div>
                <FocusInput
                  autoFocus
                  data-bind="pickContainerCodeCheck"
                  className="pickContainerCodeCheck"
                  placeholder={t('请扫描核对箱号')}
                  onChange={(e) => {
                    action.data.pickContainerCodeCheck(e.target.value?.trim());
                  }}
                  onPressEnter={(e) => {
                    if (e.target.value === (action.data.param || {}).pickContainerCode) {
                      r('ok');
                      localStorage.setItem(LOCAL_PICKING_PAGE_CODE_OBJ, '{}');
                      document.querySelectorAll('.dialog-container').forEach((v) => v.remove());
                    } else {
                      action.data.pickContainerCodeCheck();
                    }
                  }}
                />
              </div>),
            onClick: () => {
            },
          }
        ) : (
          {
            type: 'primary',
            label: t('确定'),
            onClick: () => (r('ok')),
          }
        )],
      }));
      if (status === 'ok') {
        action.data.goBack();
      }
    } else {
      yield ctx.changeData({
        data: {
          info: action.data.info.pdaTakePickTaskRsp,
        },
      });
      if (action.data.ref) {
        classFocus(`${action.data.ref.props.className}`);
      }
    }
  },
  /**
   * 获取当日的拣货数量和当前用户排名
   * @param action
   * @param ctx
   * @returns {IterableIterator<*>}
   */
  * getPickNum(action, ctx) {
    const result = yield pdaPickGoodsNum({ pickType: action.data.pickType });
    if (result.code === '0') {
      yield ctx.changeData({
        data: {
          pickCount: result.info.pickCount,
          pickTaskNums: result.info.pickTaskNums,
          waitExceptionTaskNums: result.info.waitExceptionTaskNums,
          pickingBox: result.info.pickContainerCode,
        },
      });
      const {
        yesterdayProfit,
        profitId,
      } = result.info.whetherPlanTargetProfitRsp || {};
      yield ctx.changeData({
        data: {
          profitId,
          yesterdayProfit,
        },
      });
      // whetherNeedPlanTarget为2且是正常拣货则需要弹窗
      // if (whetherNeedPlanTarget === 2 && action.data.pickType === '1') {
      //   yield ctx.changeData({ data: { targetModalVisible: true } });
      // }
    } else {
      Modal.error({ title: result.msg, className: 'pickContainerCodeInput' });
    }
    // 正常拣货
    if (action.data.pickType === '1') {
      yield ctx.getRankAndRatioInfo();
    }
  },
  // 左侧栏开启图片拣货模式
  * getOpenPicturePickingModel() {
    const {
      openPicturePickingModel,
    } = yield select((state) => state.nav);
    yield this.changeData({ data: { openPicturePickingModel } });
  },
  // 获取页面降级开关 DEGRADE_PICKING
  * getDegradeConfig(action, ctx) {
    try {
      const res = yield getApolloConfigAPI({ params: ['DEGRADE_PICKING'] });
      const apolloConfigFormatRes = apolloFormatObj(res.info);

      if (res.code === '0' && apolloConfigFormatRes?.DEGRADE_PICKING === '1') {
        yield ctx.changeData({ data: { degradeScanUpgrade: apolloConfigFormatRes?.DEGRADE_PICKING === '1' } });
      } else {
        // 获取排名。后端接口降级
        yield ctx.getRankAndRatioInfo();
      }
    } catch (e) {
      console.error(e);
    }
  },
  // 获取当前排名和效率信息 这个方法不能阻塞主流程
  * getRankAndRatioInfo(action, ctx) {
    // 如果开了降级，就不调接口
    const { degradeScanUpgrade } = yield '';
    if (degradeScanUpgrade) {
      return;
    }
    const { userName } = yield '';
    try {
      const res = yield getRankAndRatioInfoApi({ oprtType: 1, userName });
      if (res.code === '0') {
        const { isShowFlag, userRankAndRatio } = res.info;
        yield ctx.changeData({
          data: {
            isShowFlag,
            userRankAndRatio: userRankAndRatio || {},
          },
        });
      } else {
        console.log(res.msg);
      }
    } catch (e) {
      console.error(`${t('获取当前排名和效率信息错误')}: ${e}`);
    }
  },
  /**
   * PDA拣货短拣
   * @param action
   * @param ctx
   * @returns {IterableIterator<*>}
   */
  * shortPick(action, ctx) {
    markStatus('dataLoading');
    const {
      openPicturePickingModel,
    } = yield select((state) => state.nav);
    yield ctx.changeData({
      data: {
        goodsBarcodeValid: true,
      },
    });
    const { pickSeq, takePickTaskParam } = yield select((state) => state['order-picking/picking']);
    const result = yield pdaShortPick(addParam(action.data.param, pickSeq));
    if (result.code === '0') {
      // 当前拣货周转箱 已经关箱
      if (result.info.backFlag === 5) {
        const flag = yield new Promise((r) => Modal.info({
          content: result.info.tipMessage,
          onOk: () => r('ok'),
        }));
        if (flag === 'ok') {
          yield ctx.resetPageStore();
          classFocus('pickContainerCodeInput');
        }
        return;
      }
      // OFC-13792 拣货前取消商品无需拣货
      if (result.info.backFlag === 4) {
        const flag = yield new Promise((r) => Modal.info({
          content: t('任务已取消，请扫描周转箱重新领取任务'),
          onOk: () => r('ok'),
        }));
        if (flag === 'ok') {
          yield ctx.resetPageStore();
          classFocus('pickContainerCodeInput');
        }
        return;
      }
      if (result.info.backFlag === 3) {
        const { pickingPageCodeObj } = this.state;
        yield ctx.showTaskEndModal({
          data: {
            goBack: ctx.resetPageStore,
            pickContainerCodeCheck: ctx.pickContainerCodeCheck,
            param: {
              ...pickingPageCodeObj,
            },
          },
        });
        return;
      }
      yield ctx.changeData({
        data: {
          ...otherState,
          nextLocations: (result.info.pdaTakePickTaskRsp || {}).nextLocations || [],
          pickSeq,
          takePickTaskParam,
          openPicturePickingModel,
        },
      });
      const { info: { taskCode } } = yield select((v) => v['order-picking/picking']);
      const res = yield queryPickingTipApi({ taskCode });
      if (res.code === '0') {
        const pickingTip = res.info.pickingTip || '';
        yield ctx.handleBackFlag({
          data: {
            info: result.info,
            goBack: action.data.goBack,
            pickContainerCodeCheck: action.data.pickContainerCodeCheck,
            ref: action.data.ref,
            pickingTip,
            param: action.data.param,
          },
        });
      } else {
        Modal.error({
          title: res.msg,
        });
      }
      yield ctx.changeData({
        data: {
          goodsBarcodeValid: false,
        },
      });
    } else {
      yield ctx.changeData({
        data: {
          locationValid: false,
          goodsBarcodeValid: false,
          goodsBarcode: '',
          locationInput: '',
        },
      });
      const status = yield new Promise((r) => (
        Modal.error({
          title: result.msg,
          onOk: () => r('ok'),
          onCancel: () => r('cancel'),
        })
      ));
      if (status === 'ok' || status === 'cancel') {
        if (action.data.ref) {
          classFocus(`${action.data.ref.props.className}`);
        }
      }
    }
  },
  /**
   * PDA提交拣货数据
   * @param action
   * @returns {IterableIterator<*>}
   */
  * postPickData(action, ctx, put) {
    const {
      openPicturePickingModel,
    } = yield select((state) => state.nav);
    const { pickSeq, takePickTaskParam } = yield select((state) => state['order-picking/picking']);
    const result = yield pdaPostPickData(addParam(action.data.param, pickSeq));
    if (result.code === '0') {
      const { tipsIndex } = (result.info.pdaTakePickTaskRsp || {});
      // if (isReachLimit) {
      //   message.error(t('已到警戒线，禁止出现延伸箱!'));
      // }
      if ((result.info.pdaTakePickTaskRsp || {}).taskStatus === 2) {
        yield ctx.showEndModalNew();
        return;
      }
      if (tipsIndex) {
        tipAudioPlay(tipsIndex);
      }
      if (result.info.backFlag === 3) {
        const { pickingPageCodeObj } = this.state;
        yield ctx.showTaskEndModal({
          data: {
            goBack: ctx.resetPageStore,
            pickContainerCodeCheck: ctx.pickContainerCodeCheck,
            param: {
              ...pickingPageCodeObj,
            },
          },
        });
        return;
      }
      // OFC-13792 拣货前取消商品无需拣货
      if (result.info.backFlag === 4) {
        const flag = yield new Promise((r) => Modal.info({
          content: t('任务已取消，请扫描周转箱重新领取任务'),
          onOk: () => r('ok'),
        }));
        if (flag === 'ok') {
          yield ctx.resetPageStore();
          yield ctx.changeData({
            data: {
              pickContainerCodeValid: false,
              pickContainerCode: '',
            },
          });
          classFocus('pickContainerCodeInput');
        }
        return;
      }
      if (result.info.location) {
        audio.play();
        const status = yield new Promise((r) => Modal.confirm({
          title: t('当前商品已取消，请放回原库位{}', result.info.location),
          buttons: [{
            type: 'primary',
            label: t('确定'),
            onClick: () => (r('ok')),
          }],
        }));
        if (status === 'ok') {
          const { info: { taskCode } } = yield select((v) => v['order-picking/picking']);
          const res = yield taskDetailRecommend({
            pickSeq,
            taskCode,
            pickContainerCode: action.data.param.pickContainerCode,
          });
          if (res.code === '0') {
            const { taskStatus } = res.info;
            yield ctx.changeData({
              data: {
                info: assign({}, res.info, { location: res.info && res.info.location ? res.info.location.trim() : '' }),
                timeoutInfo: res.info.timeoutInfo,
                nextLocations: res.info.nextLocations,
                nationalLineTypeName: res.info.nationalLineTypeName || '',
                collectTips: res.info.collectTips || '',
                wellenTips: res.info.wellenTips || '',
                wellenTypeName: res.info.wellenTypeName || '',
              },
            });
            // OFC-13792 拣货前取消商品无需拣货
            if (taskStatus === 1) {
              const flag = yield new Promise((r) => Modal.info({
                content: t('任务已取消，请扫描周转箱重新领取任务'),
                onOk: () => r('ok'),
              }));
              if (flag === 'ok') {
                yield ctx.resetPageStore();
                yield ctx.changeData({
                  data: {
                    pickContainerCodeValid: false,
                    pickContainerCode: '',
                  },
                });
                classFocus('pickContainerCodeInput');
              }
              return;
            } else if (taskStatus === 2) {
              yield ctx.showEndModalNew();
              return;
            }
            yield ctx.changeData({
              data: {
                locationInput: '',
                locationValid: false,
                goodsBarcode: '',
                goodsBarcodeValid: false,
              },
            });
            classFocus('locationRef');
          } else {
            const s = yield new Promise((r) => Modal.error({
              title: result.msg,
              onOk: () => r('ok'),
            }));
            if (s === 'ok') {
              yield ctx.resetPageStore();
              yield ctx.changeData({
                data: {
                  pickContainerCodeValid: false,
                  pickContainerCode: '',
                },
              });
              classFocus('pickContainerCodeInput');
            }
          }
        }
        return;
      }
      yield ctx.changeData({
        data: {
          ...otherState,
          nextLocations: (result.info.pdaTakePickTaskRsp || {}).nextLocations,
          takePickTaskParam,
          pickSeq,
          openPicturePickingModel,
        },
      });
      const { info: { taskCode } } = yield select((v) => v['order-picking/picking']);
      const res = yield queryPickingTipApi({ taskCode });
      let pickingTip = '';
      if (res.code === '0') {
        pickingTip = res.info.pickingTip || '';
      } else {
        Modal.error({
          title: res.msg,
        });
        return;
      }
      // 获取排名和效益
      yield ctx.getRankAndRatioInfo();
      yield ctx.handleBackFlag({
        data: {
          info: result.info,
          goBack: action.data.goBack,
          pickContainerCodeCheck: action.data.pickContainerCodeCheck,
          ref: action.data.successRef,
          pickingTip,
          param: action.data.param,
        },
      });
    } else {
      yield ctx.changeData({
        data: {
          goodsBarcode: '',
          goodsBarcodeValid: false,
        },
      });
      audio.play();
      const status = yield new Promise((r) => Modal.error({
        title: result.msg,
        onOk: () => r('ok'),
      }));
      if (status === 'ok' && action.data.ref) {
        if (['600606', '600691'].indexOf(result.code) > -1) {
          yield ctx.init();
          const pickType = window.location.hash.endsWith('/1') ? '1' : '2';
          yield put(push(`/order-picking/picking/get-task/${pickType}`));
        } else {
          classFocus(`${action.data.ref.props.className}`);
        }
      }
    }
  },
  /**
   * 换箱操作
   * @param action
   * @param ctx
   * @returns {IterableIterator<*>}
   */
  * changePickContainer(action, ctx) {
    const {
      openPicturePickingModel,
    } = yield select((state) => state.nav);
    const { pickSeq, takePickTaskParam } = yield select((state) => state['order-picking/picking']);
    const result = yield pdaChangePickContainer(action.data.param);
    const state = yield select((v) => v['order-picking/picking']);
    if (result.code === '0') {
      const newTakePickTaskParam = {
        param: {
          pickContainerCode: action.data.param.pickContainerCode,
          pickType: action.data.param.pickType,
        },
        ref: takePickTaskParam.pickContainerCode,
      };
      yield ctx.changeData({
        data: assign({}, otherState, {
          nextLocations: state.nextLocations || [],
          pickContainerCodeDisabled: false,
          info: assign({}, action.data.info, {
            pickContainerCode: '',
            goodsBarcode: '',
          }),
          pickSeq,
          takePickTaskParam: { ...newTakePickTaskParam },
          openPicturePickingModel,
        }),
      });
      if (action.data.focus) {
        action.data.focus();
      }
    } else {
      Modal.error({
        title: result.msg,
      });
    }
    // action.data.res();
  },
  /**
   * 换箱验证
   * @param action
   * @param ctx
   * @returns {IterableIterator<*>}
   */
  * changeContainerValid(action, ctx) {
    const result = yield pdaValidPickContainer(action.data.param);
    const state = yield select((v) => v['order-picking/picking']);
    if (result.code === '0') {
      yield ctx.changeData({
        data: {
          pickContainerCodeDisabled: true,
        },
      });
      if (action.data.nextRef) {
        classFocus(`${action.data.nextRef.props.className}`);
      }
    } else {
      const status = yield new Promise((r) => Modal.confirm({
        title: result.msg,
        buttons: [{
          type: 'primary',
          label: t('确定'),
          onClick: () => (r('ok')),
        }],
      }));
      if (status === 'ok') {
        if (action.data.ref) {
          classFocus(`${action.data.ref.props.className}`);
        }
      }
      yield ctx.changeData({
        data: {
          pickContainerCodeDisabled: false,
          info: assign({}, state.info, {
            pickContainerCode: '',
          }),
        },
      });
    }
  },
  * getDetail(action, ctx, put) {
    const { pickSeq } = yield select((state) => state['order-picking/picking']);
    markStatus('dataLoading');
    const res = yield getDetailApi(addParam(action.params, pickSeq));
    if (res.code === '0') {
      yield put((draft) => {
        draft.showDetail = true;
        draft.detailList = [res.info.hasPickList || [], res.info.waitingPickList || []];
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  * validateTask(action, ctx) {
    const { param, cb } = action;
    const res = yield pdaValidateTaskApi(param);
    if (res.code === '0') {
      const pickingTip = res.info.pickingTip || '';
      yield ctx.changeData({ data: { pickingTip } });
      if (res.info.backFlag === 1) {
        const { pickingPageCodeObj } = this.state;
        yield ctx.showTaskEndModal({
          data: {
            goBack: ctx.resetPageStore,
            pickContainerCodeCheck: ctx.pickContainerCodeCheck,
            param: {
              ...pickingPageCodeObj,
            },
          },
        });
      } else if (cb) {
        cb(pickingTip);
      }
    } else {
      Modal.error({
        title: res.msg,
        className: 'locationRef',
      });
    }
  },
  * showTaskEndModal(action, ctx, put) {
    const {
      info: {
        taskCode,
        pickContainerCode,
      },
      nationalLineTypeName,
      collectTips,
      wellenTips,
    } = yield select((v) => v['order-picking/picking']);
    if (pickContainerCode && taskCode) {
      localStorage.setItem(LOCAL_PICKING_PAGE_CODE_OBJ, JSON.stringify({
        pickContainerCode,
        taskCode,
      }));
      pushStateFn();
    }
    const status = yield new Promise((r) => Modal.info({
      content: (
        <div>
          {t('已到下班时间，当前商品操作失败，箱子已关箱，请投线！')}
          {collectTips && <div>{t('集货提示')}：{collectTips}</div>}
          {wellenTips && <div>{t('波次提示')}：{wellenTips}</div>}
          <br />
          {
            nationalLineTypeName && (
              <div className={style.nationalLine} style={{ display: 'inline' }}>{nationalLineTypeName}</div>
            )
          }
          {pickContainerCode && <div>{t('拣货周转箱')}：{pickContainerCode}</div>}
        </div>
      ),
      buttons: [pickContainerCode ? (
        {
          type: 'primary',
          noClose: true,
          label: (
            <div>
              <FocusInput
                autoFocus
                data-bind="pickContainerCodeCheck"
                className="pickContainerCodeCheck"
                placeholder={t('请扫描核对箱号')}
                onChange={(e) => {
                  action.data.pickContainerCodeCheck(e.target.value);
                }}
                onPressEnter={(e) => {
                  if (e.target.value === pickContainerCode) {
                    r('ok');
                    localStorage.setItem(LOCAL_PICKING_PAGE_CODE_OBJ, '{}');
                    document.querySelectorAll('.dialog-container').forEach((v) => v.remove());
                  } else {
                    action.data.pickContainerCodeCheck();
                  }
                }}
              />
            </div>),
          onClick: () => {
          },
        }
      ) : (
        {
          type: 'primary',
          label: t('确定'),
          onClick: () => (r('ok')),
        }
      )],
    }));
    if (status === 'ok') {
      action.data.goBack();
    }
    if (status === 'ok') {
      yield ctx.init();
      yield put(push('/order-picking/picking/get-task/1'));
    }
  },
  // 跳过库位
  * skipLocation(action, ctx) {
    const { infoParams, locationInput } = action;
    const { pickSeq } = yield select((state) => state['order-picking/picking']);
    const { code, info, msg } = yield skipLocationApi(addParam(infoParams, pickSeq));
    if (code === '0') {
      const {
        sequence,
        isLocation,
        nextLocations,
        isHandover,
        pickingTip,
        operateTip,
        nationalLineTypeName,
        taskStatus,
      } = info || {};
      // 取消流程
      if (taskStatus === 1) {
        const flag = yield new Promise((r) => Modal.info({
          content: t('任务已取消，请扫描周转箱重新领取任务'),
          onOk: () => r('ok'),
        }));
        if (flag === 'ok') {
          yield ctx.resetPageStore();
          classFocus('pickContainerCodeInput');
        }
        return;
      } else if (taskStatus === 2) {
        yield ctx.showEndModalNew();
        return;
      }
      // 修改页面数据
      yield ctx.changeData({
        data: {
          sequence,
          isLocation,
          nextLocations,
          locationValid: false,
          locationInput: '',
        },
      });
      // 修改info数据
      yield ctx.changeCurrent({
        data: info,
      });
      if (isHandover === 1) {
        Modal.info({
          content: (
            <div style={{ textAlign: 'center' }}>
              <div>{operateTip}</div>
              {
                pickingTip && (
                  <div style={{ color: 'red' }}>{pickingTip}</div>
                )
              }
              {
                nationalLineTypeName && (
                  <div className={style.nationalLine}>{nationalLineTypeName}</div>
                )
              }
            </div>
          ),
          className: 'locationRef',
        });
      }
      // classFocus('locationRef');
    } else {
      Modal.error({
        title: msg,
        className: locationInput ? 'goodsBarcode' : 'locationRef',
      });
    }
  },
  // 设定目标
  * modifyTarget(action, ctx) {
    const { haveProfit } = yield select((v) => v['order-picking/picking']);
    const res = yield modifyTargetApi(action.params);
    if (res.code === '0') {
      // 目标设置成功之后，光标跳转至拣货周转箱
      classFocus('pickContainerCodeInput');
      // 设定目标后，计算目标进度
      yield ctx.changeData({
        data: {
          targetProcess: ((haveProfit / action.params.details[0].profitTarget) * 100).toFixed(2),
        },
      });
    }
  },
  * queryPickingTip(action, ctx) {
    const res = yield queryPickingTipApi(action.params);
    if (res.code === '0') {
      const pickingTip = res.info.pickingTip || '';
      yield ctx.changeData({ data: { pickingTip } });
      return Promise.resolve(pickingTip);
    } else {
      Modal.error({
        title: res.msg,
      });
      return Promise.reject();
    }
  },
  * pickContainerCodeCheck(action, ctx) {
    const val = action;
    if (val && typeof val !== 'object') {
      yield ctx.changeData({
        data: {
          pickContainerCodeCheck: val,
        },
      });
    } else {
      audio.play();
      message.error(t('请扫描正确的核对箱号'), 2000);
      document.querySelectorAll('.pickContainerCodeCheck')?.forEach((element) => {
        element.value = '';
      });
      classFocus('pickContainerCodeCheck');
      yield ctx.changeData({
        data: {
          pickContainerCodeCheck: '',
        },
      });
    }
  },

  /**
   * 扫描库位号
   * @param action
   * @param ctx
   * @returns
   */
  * scanLocationCode(action, ctx) {
    const {
      info,
      locationInput,
      pickContainerCodeDisabled,
      collectTips,
      wellenTips,
      wellenTypeName,
      nationalLineTypeName,
    } = yield '';
    const { location, pickContainerCode, taskCode } = info;
    const {
      openPicturePickingModel,
    } = yield 'nav';
    const { pickType, focus } = action;
    // 判断任务成功之后再走其他逻辑
    // 库位和推荐库位不一致
    // 非图片拣货模式才允许在库位号处换箱
    if (location !== locationInput &&
      (openPicturePickingModel || !isStrEqual(pickContainerCode, locationInput))) {
      Modal.error({ modalBlurInput: true, title: t('库位与需拣货库位不一致'), className: 'locationRef' });
      yield ctx.changeData({ data: { locationInput: '' } });
      audio.play();
    } else {
      // >>> 为佛山仓
      // >>> 库位和周转箱一致 上面判断了
      // >>> 执行换箱
      if (!openPicturePickingModel && pickContainerCodeDisabled &&
        isStrEqual(pickContainerCode, locationInput)) {
        const status = yield new Promise((r) => Modal.confirm({
          modalBlurInput: true,
          content: (
            <div>
              <div>{t('确认是否换箱？')}</div>
              {collectTips && <div>{t('集货提示')}：{collectTips}</div>}
              {wellenTips && <div>{t('波次提示')}：{wellenTips}</div>}
              {wellenTypeName && <div>{t('波次类型')}：{wellenTypeName}</div>}
              <div>
                {nationalLineTypeName && (
                  <span className={style.nationalLine}>
                    {nationalLineTypeName}
                  </span>
                )}
              </div>
            </div>
          ),
          onOk: () => r('ok'),
          onCancel: () => r('cancel'),
        }));
        if (status === 'ok') {
          // 执行换箱
          yield ctx.changePickContainer({
            data: {
              param: {
                pickContainerCode,
                taskCode,
                pickType,
                warehouseId: (JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}') || {}).warehouseId,
              },
              info,
              focus,
            },
          });
        } else {
          yield ctx.changeData({ data: { locationInput: '' } });
          classFocus('locationRef');
        }
        return;
      }
      yield ctx.changeData({ data: { locationValid: true } });
      // store.scanLocation({
      //   pickType,
      // });
      classFocus('goodsBarcode');
    }
  },

  /**
   * 图片拣货模式下操作'换箱'按钮
   * @returns {Generator<*, void, *>}
   */
  * handleChangeBox(action, ctx, put) {
    const { info: { taskCode, pickContainerCode } } = yield '';
    markStatus('dataLoading');
    const res = yield changeOldContainerConfirmAPI({
      pickContainerCode,
      pickType: 0,
      taskCode,
      warehouseId: getWarehouseId(),
    });
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          boxInfo: res.info || {},
          changeBoxFlag: OLD_BOX,
          showChangeBox: true,
        },
      });
      classFocus('boxCode');
    } else if (res.code === '061001') {
      // 若该拣货箱已关箱，则点击确认返回领取任务页面
      const status = yield new Promise((r) => Modal.error({
        title: res.msg,
        modalBlurInput: true,
        onOk: () => r('ok'),
      }));
      if (status === 'ok') {
        // 返回任务领取页面并初始化数据
        yield ctx.init();
        yield put(push('/order-picking/picking/get-task/1'));
      }
    } else {
      Modal.error({
        oldContainerCode: '',
        title: res.msg,
        modalBlurInput: true,
        className: 'boxCode',
      });
    }
  },

  /**
   * 换箱页面扫描箱号，根据isNew判断是扫描旧箱号还是新箱号
   * @returns {Generator<*, void, *>}
   */
  * confirmBoxCode(action, ctx) {
    // 判断箱号是否一致
    const { boxInfo: { pickContainerCode } } = yield '';
    const {
      openPicturePickingModel,
    } = yield 'nav';
    const { boxCode, isNewBox, pickType } = action;
    // 若为输入旧箱号，则判断输入箱号与当前拣货箱号是否相等
    if (!isNewBox && boxCode !== pickContainerCode) {
      const status = yield new Promise((r) => Modal.error({
        title: t('箱号错误'),
        className: 'boxCode',
        modalBlurInput: true,
        onOk: () => r('ok'),
      }));
      if (status === 'ok') {
        yield ctx.changeData({
          data: {
            oldContainerCode: '',
          },
        });
      }
    } else {
      // 扫描旧箱号，调至新箱确认页面
      if (!isNewBox) {
        yield this.changeData({
          data: {
            changeBoxFlag: NEW_BOX,
          },
        });
      } else {
        // 扫描新箱号，请求新箱确认接口
        const {
          newContainerCode, oldContainerCode, info, lastFocus,
          locationInput,
          takePickTaskParam,
          pickSeq,
        } = yield '';
        const { taskCode } = info;
        const params = {
          newContainerCode,
          oldContainerCode,
          taskCode,
          pickType,
          warehouseId: getWarehouseId(),
        };
        markStatus('dataLoading');
        const res = yield confirmChangeNewBoxAPI(params);
        if (res.code === '0') {
          // 因成功换箱，需更新部分数据
          const newTakePickTaskParam = {
            param: {
              pickContainerCode: newContainerCode,
              pickType,
            },
            ref: takePickTaskParam.pickContainerCode,
          };
          yield ctx.changeData({
            data: {
              info: assign({}, info, {
                pickContainerCode: newContainerCode,
              }),
              pickSeq,
              takePickTaskParam: { ...newTakePickTaskParam },
              openPicturePickingModel,
            },
          });
          // 扫描新箱成功后返回拣货页面
          yield this.resetChangeBoxInfo();

          // 若原先聚焦在库位且库位号有值，则自动触发一次扫描库位
          if (lastFocus === 'locationRef' && locationInput) {
            yield this.scanLocationCode({
              pickType,
              focus: () => {
                requestAnimationFrame(() => {
                  classFocus('pickContainerCode');
                });
              },
            });
          } else {
            classFocus(lastFocus);
          }
        } else {
          const status = yield new Promise((r) => Modal.error({
            title: res.msg,
            className: 'boxCode',
            modalBlurInput: true,
            onOk: () => r('ok'),
          }));
          if (status === 'ok') {
            yield ctx.changeData({
              data: {
                newContainerCode: '',
              },
            });
          }
        }
      }
    }
  },
};
