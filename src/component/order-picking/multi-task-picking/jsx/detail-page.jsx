// detail-page 明细

import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import classnames from 'classnames';
import Icon from '@shein-components/Icon';
import { Form } from 'react-weui/build/packages/components/form';
import { Tab, NavBar, NavBarItem } from 'react-weui/build/packages';
import { Footer, Table } from '../../../common/index';
import store from '../reducers';
import styles from '../style.less';
import { getLang } from '../../../js';

const isChinese = getLang() === 'zh';
function DetailPage(props) {
  const {
    activeNavKey,
    activeHasPickKeys,
    activeNoPickKeys,
    waitPickList,
    pickedList,
    // recommendInfo,
    pickedDetailInfo,
    waitPickDetailInfo,
    waitPickedNum,
    pickedNum,
  } = props;
  const totalNumList = [pickedNum, waitPickedNum];
  const navList = [t('已拣选'), t('未拣选')];
  return (
    <div>
      {/* TODO 不用标准的组件NavDetail是因为NavDetail列表只能一行一行，与需求有差异 */}
      {/* <NavDetail
        data={data}
        rowsList={rowsList}
        navList={navList}
        imgUrlFieldName="imageUrl"
        totalNumList={totalNumList}
      /> */}
      <div style={{ height: 34 }}>
        <Tab>
          <NavBar>
            {
                navList.map((item, index) => (
                  <NavBarItem
                    // eslint-disable-next-line react/no-array-index-key
                    key={index}
                    onClick={() => {
                      store.changeData({
                        activeNavKey: index,
                      });
                      // if (index === 1) {
                      //   store.getWaitPick(recommendInfo.taskSeq);
                      // } else {
                      //   store.getPickList(recommendInfo.taskSeq);
                      // }
                    }}
                    active={index === activeNavKey}
                  >
                    <span style={{ color: index === activeNavKey ? '#197AFA' : '' }}>
                      {item}({totalNumList[index]})
                    </span>
                  </NavBarItem>
                ))
              }
          </NavBar>
        </Tab>
      </div>
      <div style={{ height: 'calc(100vh - 145px)', overflowY: 'auto' }}>
        {/* 已拣选 */}
        {
          activeNavKey === 0 &&
          pickedList.map((item, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <div key={index}>
              <div
                className={styles.detailListItem}
                style={index === 0 ? { marginTop: '0px' } : {}}
                onClick={() => {
                  if (activeHasPickKeys.includes(index)) {
                  // 打开情况下，关闭
                  // 移除元素
                    const findIndex = activeHasPickKeys.findIndex((i) => i.index);
                    activeHasPickKeys.splice(findIndex, 1);
                    store.changeData({
                      activeHasPickKeys: [...activeHasPickKeys],
                    });
                  } else {
                  // 关闭情况下，开启
                    const { pickContainerId, taskCode } = item;
                    // if (!pickedDetailInfo[item.taskCode]) {
                    //   store.getPickedDetail({ pickContainerId, taskCode });
                    // }
                    // 后台说点击一次调一次接口刷新下数据，会有取消的场景需要刷新数据
                    store.getPickedDetail({ pickContainerId, taskCode });
                    store.changeData({
                      activeHasPickKeys: [...activeHasPickKeys, index],
                    });
                  }
                }}
              >
                <div className={classnames(styles.detailListIcon, styles[item.taskSeq === 1 ? 'numberOne' : 'numberTwo'])}>
                  <div>{t('周转箱：')}{item.pickContainerCode}</div>
                  <div>
                    {/* 解决【任务单号】英文翻译过来多一个【：】的问题 */}
                    <span>{isChinese ? `${t('任务单号')}：` : t('任务单号')}</span> {item.taskCode}
                  </div>
                </div>
                <div className={classnames(styles.detailListNum, styles[item.taskSeq === 1 ? 'numberOne' : 'numberTwo'])}>
                  <span className={styles.numText}>{t('{}号', item.taskSeq)}</span>
                  <Icon name={activeHasPickKeys.includes(index) ? 'arr-down' : 'arr-right'} className={styles.showArrow} />
                </div>
              </div>
              {/* 点击展开展示的表格 */}
              {activeHasPickKeys.includes(index) && (
              <Form>
                <Table
                  columns={[
                    {
                      title: <div className={styles.leftAlign}>SKC/{t('尺码')}</div>,
                      dataIndex: 'skcCode',
                      width: 15,
                      render: (row) => (
                        <div className={styles.leftAlign}>
                          <span>{row.skcCode}</span>
                          /
                          <span>{row.size}</span>
                        </div>
                      ),
                    },
                    {
                      title: t('数量'),
                      dataIndex: 'num',
                      width: 5,
                      render: (row) => (
                        /* eslint-disable */
                        <a
                          onClick={() => {
                            store.getImage(row)
                          }}
                          className={styles.dichotomyStationA}
                        >{row.num}
                        </a>
                        /* eslint-disable */
                      ),
                    },
                  ]}
                  dataSource={pickedDetailInfo[item.taskCode] || []}
                />
              </Form>
              )}
            </div>
          ))
        }
        {/* 未拣选 */}
        {
          activeNavKey === 1 &&
          waitPickList.map((item, index) => (
            <div key={index}>
              <div
                className={styles.detaiNoPicklListItem}
                style={index === 0 ? { marginTop: '0px' } : {}}
                onClick={() => {
                  if (activeNoPickKeys.includes(index)) {
                    // 打开情况下，关闭
                    // 移除元素
                    const findIndex = activeNoPickKeys.findIndex((i) => i.index);
                    activeNoPickKeys.splice(findIndex, 1);
                    store.changeData({
                      activeNoPickKeys: [...activeNoPickKeys],
                    });
                  } else {
                    // 关闭情况下，开启
                    const { taskCode } = item;
                    // if (!waitPickDetailInfo[item.taskCode]) {
                    //   store.getWaitPickDetail(taskCode);
                    // }
                    // 后台说点击一次调一次接口刷新下数据，会有取消的场景需要刷新数据
                    store.getWaitPickDetail(taskCode);
                    store.changeData({
                      activeNoPickKeys: [...activeNoPickKeys, index],
                    });
                  }
                }}
              >
                <div className={classnames(styles.detailNoPickListIcon, styles[item.taskSeq === 1 ? 'numberOne' : 'numberTwo'])}>
                  <span>{t('任务单号')}：</span> {item.taskCode}
                </div>
                <Icon name={activeNoPickKeys.includes(index) ? 'arr-down' : 'arr-right'} className={styles.showArrow} />
              </div>
                 {/* 点击展开展示的表格 */}
              {activeNoPickKeys.includes(index) && (
              <Form>
                <Table
                  columns={[
                    {
                      title: <div className={styles.leftAlign}>
                        <div>
                         SKC/{t('尺码')}
                        </div>
                        <div>
                          {t('库位')}
                        </div>
                             </div>,
                      dataIndex: 'skcCode',
                      width: 15,
                      render: (d) => (
                        <div className={styles.leftAlign}>
                          <div>
                           <span> {d.skcCode}</span>
                           /
                           <span>{d.size}</span>
                          </div>
                          <div>
                            {d.location}
                          </div>
                        </div>
                      ),
                    },
                    {
                      title: t('数量'),
                      dataIndex: 'num',
                      width: 5,
                      render: (row) => (
                        /* eslint-disable */
                        <a
                          onClick={() => {
                            store.getImage(row)
                          }}
                          className={styles.dichotomyStationA}
                        >{row.num}
                        </a>
                        /* eslint-disable */
                      ),
                    },
                  ]}
                  dataSource={waitPickDetailInfo[item.taskCode] || []}
                />
              </Form>
              )}
            </div>
          ))
        }
      </div>
      <Footer
        beforeBack={() => {
            // 跳回到执行页面
            store.changeData({
            resultType: '2',
          });
        }}
      />
    </div>
  );
}

DetailPage.propTypes = {
  activeNavKey: PropTypes.number,
  waitPickedNum: PropTypes.number,
  pickedNum: PropTypes.number,
  activeHasPickKeys: PropTypes.arrayOf(PropTypes.number),
  activeNoPickKeys: PropTypes.arrayOf(PropTypes.number),
  waitPickList: PropTypes.arrayOf(PropTypes.shape()),
  pickedList: PropTypes.arrayOf(PropTypes.shape()),
  recommendInfo: PropTypes.shape(),
  pickedDetailInfo: PropTypes.shape(),
  waitPickDetailInfo: PropTypes.shape(),
};

export default DetailPage;
