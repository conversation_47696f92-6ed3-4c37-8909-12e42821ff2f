import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import {
  FocusInput, Footer, FooterBtn,
} from 'common';
import classnames from 'classnames';
import style from 'common/common.css';
import store from '../reducers';
import styles from '../style.less';
import { getLang } from '../../../js';

const isChinese = getLang() === 'zh';

// 任务状态枚举 1-待领取，2-拣货中，3-拣货完成
const taskStatusNameEnum = [
  '',
  t('待领取'),
  t('拣货中'),
  t('拣货完成'),
];

function InitPage(props) {
  const {
    pickContainerCode,
    pickContainerDisabled,
    pickingList,
  } = props;
  // 不存在待领取任务
  const noStashTask = pickingList.every((item) => item.taskStatus !== 1) &&
                      pickingList.some((item) => item.taskStatus === 2);
  return (
    <div>
      <Form className={style.pageStikyChild}>
        <FocusInput
          placeholder={t('请扫描')}
          autoFocus
          value={pickContainerCode}
          // 用户存在拣货组合任务，且组合任务中不存在待领取任务时，周转箱输入框不可用
          disabled={pickContainerDisabled === 0 || noStashTask}
          className="pickContainerCode"
          onChange={(e) => {
            store.changeData({
              pickContainerCode: e.target.value.trim(),
            });
          }}
          onPressEnter={() => {
            if (pickContainerCode) {
              store.scanContainer(pickContainerCode);
            }
          }}
        >
          <label>{t('拣货周转箱')}</label>
        </FocusInput>
      </Form>
      {/* <div className={styles.taskTitle}>{t('2个待拣货任务')}</div> */}
      <div className={styles.pickingListPannel}>
        {
          pickingList.map((item, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <div className={styles.listItem} key={index}>
              <div>
                <div className={classnames(styles.listIcon, styles[item.order === 1 ? 'numberOne' : 'numberTwo'])}>
                  <span className={styles.itemStatus}>{taskStatusNameEnum[item.taskStatus]}</span>
                  {item.pickContainerCode}
                </div>
                <div>
                  {/* 解决【任务单号】英文翻译过来多一个【：】的问题 */}
                  <span>{isChinese ? `${t('任务单号')}：` : t('任务单号')}</span> {item.taskCode}
                </div>
              </div>
              {/* 待领取不展示顺序号 */}
              { item.taskStatus !== 1 && <div className={classnames(styles.listNum, styles[item.order === 1 ? 'numberOne' : 'numberTwo'])}>{t('{}号', item.order)}</div>}
            </div>
          ))
      }
      </div>
      <Footer>
        {noStashTask && (
          <FooterBtn
            onClick={() => {
              store.getPickingTask('init-page');
            }}
          >
            {t('继续拣货')}
          </FooterBtn>
        )}
      </Footer>
    </div>
  );
}

InitPage.propTypes = {
  pickContainerCode: PropTypes.string,
  pickContainerDisabled: PropTypes.number,
  pickingList: PropTypes.arrayOf(PropTypes.shape()),
};

export default InitPage;
