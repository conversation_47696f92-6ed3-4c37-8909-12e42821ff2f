import React from 'react';
import PropTypes from 'prop-types';
import { Form, Button } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import {
  classFocus,
} from 'lib/util';
import {
  FocusInput, Footer, FooterBtn,
} from 'common';
import classnames from 'classnames';
import audio from 'lib/audio';
import Modal from '../../../common/modal';
import store from '../reducers';
import styles from '../style.less';
import { getLang } from '../../../js';

const isChinese = getLang() === 'zh';

function PerformTaskPage(props) {
  const {
    recommendInfo,
    location,
    goodsBarcode,
    goodsBarcodeDisabled,
    pickContainerDisabled,
    locationDisabled,
    locationCantInput,
    pickContainerCode,
    showTotal,
    pickContainerDisabledFlag,
    goodsBarcodeDisabledFlag,
  } = props;
  const {
    taskCode, nationalLineTypeName, totalGoodsNum, hadPickNum, location: recommendLocation,
    size, nextLocations, taskOrder, goodsSn, pickNum, locationHadPickNum,
  } = recommendInfo;
  const getColor = (info) => (isChinese ? info.colorZh : info.colorEn);
  const splitLocations = recommendLocation?.split('-');
  return (
    <div className={styles.taskPannel} id="task-pannel">
      <div className={styles.firstItem}>
        <div className={styles.topOne} onClick={() => store.changeData({ showTotal: !showTotal })}>
          <span>{taskCode}</span>
          {nationalLineTypeName &&
          <span className={styles.nationalLine}>{nationalLineTypeName}</span>}
          <Icon name={showTotal ? 'arr-down' : 'arr-right'} className={styles.showArrow} />
        </div>
        {showTotal && (
        <div className={styles.topTwo}>
          <span className={styles.totalRankText}> {t('已拣/总件数')}:</span>
          <span className={styles.totalRank}>
            <span className={styles.pickNum}>{hadPickNum}</span>
            /{totalGoodsNum}
          </span>
        </div>
        )}

      </div>
      <div className={styles.secondItem}>
        <div className={styles.seconedline}>
          <span>
            {splitLocations && splitLocations[0]}{splitLocations && splitLocations.length > 1 && '-'}
          </span>
          <span className={styles.locationStyle}>
            { splitLocations && splitLocations.filter((_, i) => i > 0).join('-')}
          </span>
        </div>
        <div className={classnames(styles.seconedline, styles.secondCenter)}>
          {goodsSn}
        </div>
        <div className={styles.seconedline}>
          <div className={styles.secondBottom}>
            <div className={styles.secondBottomLeft}>{getColor(recommendInfo)}{getColor(recommendInfo) && '/'}{size}</div>
          </div>
          <div>
            <div className={styles.secondBottomRight}>
              <span className={styles.totalRankText}>{t('已拣/总件数')}:</span>
              <span className={styles.totalRank}>
                <span className={styles.pickNum}>{locationHadPickNum}</span>
                /{pickNum}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className={styles.thirdItem}>
        {/* 取第一个’-‘后面的数据 */}
        {nextLocations?.filter((_, index) => index < 3)
          // eslint-disable-next-line react/no-array-index-key
          .map((e, indexMap) => <span key={indexMap} className={classnames(styles.flexOne, styles[indexMap === 0 ? 'locationWeight' : ''])}>{e?.split('-').filter((_, i) => i > 0).join('-')}</span>)}
      </div>
      {/* <div className={styles.arrowBlock}>
        <div className={classnames(styles.arrowItem,
           styles[taskOrder === 1 ? 'numberOneBackground' : 'numberTwoBackground'])}>
          <div className={styles.arrowItemInterBorder}>{taskOrder}{t('号')}</div>
        </div>
      </div> */}
      <div className={classnames(styles.arrowStyle, styles[taskOrder === 1 ? 'oneBackground' : 'twoBackground'])}>
        {t('{}号', taskOrder)}
      </div>
      <Form>
        <FocusInput
          importance
          disabled={pickContainerDisabled === 0 || pickContainerDisabledFlag}
          value={pickContainerCode}
          className="pickContainerCode"
          onChange={(e) => {
            store.changeData({
              pickContainerCode: e.target.value.trim(),
            });
          }}
          onPressEnter={() => {
            if (pickContainerCode) {
              store.pickContainerCheck(pickContainerCode);
            }
          }}
          footer={(
            <Button
              size="small"
              className={classnames(styles[isChinese ? 'lackGoodsBtnZh' : 'lackGoodsBtnEn'], styles[taskOrder === 1 ? 'numberOneBackground' : 'numberTwoBackground'])}
              onClick={() => {
                // 跳转到换箱页面
                store.changePickContainer(0);
              }}
            >
              {t('换箱')}
            </Button>
        )}
        >
          <label>{t('周转箱')}</label>
        </FocusInput>
        <FocusInput
          importance
          autoFocus
          value={location}
          disabled={locationDisabled === 0 || locationCantInput}
          className="location"
          onChange={(e) => {
            store.changeData({
              location: e.target.value.trim(),
            });
          }}
          onPressEnter={() => {
            if (location) {
              if (location !== recommendLocation) {
                Modal.error({ title: t('请扫描需拣货的正确库位') });
                store.changeData({ location: '' });
                classFocus('location');
                audio('error', 'play');
                return;
              }
              store.scanLocation(location);
            }
          }}
          footer={(
            <Button
              size="small"
              className={classnames(styles[isChinese ? 'lackGoodsBtnZh' : 'lackGoodsBtnEn'], styles[taskOrder === 1 ? 'numberOneBackground' : 'numberTwoBackground'])}
              onClick={() => {
                store.skipLocation();
              }}
            >
              {t('跳过')}
            </Button>
        )}
        >
          <label>{t('库位号')}</label>
        </FocusInput>
        <FocusInput
          importance
          value={goodsBarcode}
          disabled={goodsBarcodeDisabled === 0 || goodsBarcodeDisabledFlag}
          className="goodsBarcode"
          onChange={(e) => {
            store.changeData({
              goodsBarcode: e.target.value.trim(),
            });
          }}
          onFocus={() => {
            // 商品条码焦距条码时，滚动到底部
            const ele = document.getElementById('task-pannel');
            ele.scrollTop = ele.scrollHeight;
          }}
          onPressEnter={() => {
            if (goodsBarcode) {
              store.scanGoodsBarcode(goodsBarcode);
            }
          }}
        >
          <label>{t('商品条码')}</label>
        </FocusInput>
      </Form>
      <Footer
        footerBtnClass={classnames(styles[taskOrder === 1 ? '' : 'footerBtnGreenClass'])}
        beforeBack={() => {
          // 跳回到初始化页面
          store.changeData({
            resultType: '1',
            pickContainerCode: '',
            recommendInfo: {},
          });
          store.getPickingBox();
          classFocus('pickContainerCode');
        }}
      >
        <FooterBtn
          className={classnames(styles[taskOrder === 1 ? 'numberOneBackground' : 'numberTwoBackground'])}
          onClick={() => {
            // 跳转到明细页面
            store.changeData({
              resultType: '3',
              activeHasPickKeys: [],
              activeNoPickKeys: [],
            });
            store.getPickList(taskOrder);
            store.getWaitPick(taskOrder);
          }}
        >
          {t('明细')}
        </FooterBtn>
      </Footer>
    </div>
  );
}

PerformTaskPage.propTypes = {
  recommendInfo: PropTypes.shape(),
  location: PropTypes.string,
  locationDisabled: PropTypes.number,
  locationCantInput: PropTypes.bool,
  goodsBarcodeDisabled: PropTypes.number,
  pickContainerDisabled: PropTypes.number,
  goodsBarcode: PropTypes.string,
  pickContainerCode: PropTypes.string,
  showTotal: PropTypes.bool,
  pickContainerDisabledFlag: PropTypes.bool,
  goodsBarcodeDisabledFlag: PropTypes.bool,
};

export default PerformTaskPage;
