import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  FocusInput, Footer,
} from 'common';
import {
  classFocus,
} from 'lib/util';
import audio from 'lib/audio';
import store from '../reducers';
import styles from '../style.less';
import CountDown from '../../../common/count-down';

const playAudio = function () {
  audio('warnSound', 'play');
  setTimeout(() => audio('warnSound', 'pause'), 5000);
};
let warningTimer = null;
let warningTimer2 = null;
const onEnd = function (oldTime, step) {
  playAudio();
  if (warningTimer === null) {
    if (oldTime < 0) {
      warningTimer2 = setTimeout(() => {
        playAudio();
        warningTimer = setInterval(() => playAudio(), step * 1000);
      }, (step - (-oldTime % 60)) * 1000);
    } else {
      warningTimer = setInterval(() => playAudio(), step * 1000);
    }
  }
};
const onSoon = function () {
  playAudio();
};

class ChangeBoxPage extends Component {
  componentWillUnmount() {
    if (warningTimer) {
      clearInterval(warningTimer);
      warningTimer = null;
    }
    if (warningTimer2) {
      clearTimeout(warningTimer2);
      warningTimer2 = null;
    }
  }

  render() {
    const { recommendInfo, changePickInfo } = this.props;
    const {
      timeoutInfo, nationalLineTypeName, taskCode, hadPickNum,
      totalGoodsNum,
    } = recommendInfo;
    const { emptyMessage, collectTips, wellenTips } = changePickInfo;
    const {
      newPickContainerCode,
      newPickContainerDisabled,
    } = this.props;
    // const timeoutInfo = {
    //   timeoutSeconds: 3600, // 超时再响频率时长
    //   soonOvertimeSeconds: 1200, // 即将超时时长
    //   overtimeAgainSeconds: 300, // 超时秒数
    // };
    const displayCountDown = (datas) => {
      if (!datas) {
        return null;
      }
      const {
        timeoutSeconds, // 超时再响频率时长
        soonOvertimeSeconds, // 即将超时时长
        overtimeAgainSeconds, // 超时秒数
      } = datas;
      return (-timeoutSeconds > 24 * 60 * 60) ? (
        <div
          style={{
            backgroundColor: '#d8e2f7',
          }}
        >
          {t('该任务时长超过24小时')}
        </div>
      ) : (
        <CountDown
          countLabelText={`${t('该任务还剩')}:`}
          timeoutSeconds={timeoutSeconds}
          soonOvertimeSeconds={soonOvertimeSeconds}
          onEnd={() => onEnd(timeoutSeconds, overtimeAgainSeconds)}
          onSoon={() => onSoon()}
        />
      );
    };
    return (
      <div>
        <div className={styles.firstItem}>
          <span>{taskCode}</span>
          {nationalLineTypeName &&
          <span className={styles.nationalLine}>{nationalLineTypeName}</span>}
          <div>
            <span className={styles.totalRankText}> {t('已拣/总件数')}:</span>
            <span className={styles.totalRank}>
              <span className={styles.pickNum}>{hadPickNum}</span>
              /{totalGoodsNum}
            </span>
          </div>
        </div>
        <div className={styles.tipsBox}>
          {displayCountDown(timeoutInfo)}
        </div>
        <div className={styles.midddleText}>
          {
           emptyMessage ?
             <div>{emptyMessage}</div>
             : (
               <div>
                 <div>{t('集货提示')}：{collectTips}</div>
                 <div>{t('波次提示')}：{wellenTips}</div>
               </div>
             )
          }
        </div>
        <FocusInput
          placeholder={t('请扫描新的周转箱')}
          autoFocus
          className="newPickContainerCode"
          disabled={newPickContainerDisabled === 0}
          onChange={(e) => {
            store.changeData({
              newPickContainerCode: e.target.value.trim(),
            });
          }}
          onPressEnter={() => {
            if (newPickContainerCode) {
              store.changePickContainer(1);
            }
          }}
        >
          <label>{t('请扫描新的周转箱')}</label>
        </FocusInput>
        <Footer
          beforeBack={() => {
            // 跳转到换箱页面 焦点定位: 库位号
            store.changeData({
              resultType: '2',
            });
            classFocus('location');
          }}
        />
      </div>
    );
  }
}

ChangeBoxPage.propTypes = {
  newPickContainerCode: PropTypes.string,
  newPickContainerDisabled: PropTypes.number,
  recommendInfo: PropTypes.shape(),
  changePickInfo: PropTypes.shape(),
};

export default ChangeBoxPage;
