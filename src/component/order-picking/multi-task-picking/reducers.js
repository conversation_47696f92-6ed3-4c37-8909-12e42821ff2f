import React from 'react';
import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { markStatus } from 'rrc-loader-helper';
import {
  getHeaderTitle, classFocus,
} from 'lib/util';
import { message, modal } from 'common';
import audio from 'lib/audio';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import classnames from 'classnames';
import {
  getPickingBoxAPI, takePickTaskAPI, listPickedAPI, listWaitPickAPI, postPickDataAPI, shortPickAPI,
  pickedDetailAPI, taskDetailRecommendAPI, changePickContainerAPI, waitPickDetailAPI,
  pickContainerCheckAPI, takeSkipLocationAPI, scanLocationAPI, getImageAPI, getPickingTaskAPI,
} from './server';
import styles from './style.less';

const defaultState = {
  pickingList: [], // 拣货中的周转箱
  pickContainerCode: '', // 拣货周转箱
  pickContainerDisabled: 1, // 扫描拣货周转箱disabled 请求时
  pickContainerDisabledFlag: true, // 扫描拣货周转箱disabled（执行任务页面）
  goodsBarcodeDisabledFlag: true, // 扫描条码disabled（执行任务页面）
  recommendInfo: {}, // 推荐信息,执行任务页面的数据展示
  waitPickList: [], // 未拣列表
  waitPickedNum: 0, // 未拣数量
  waitPickDetailInfo: {}, // 未拣明细
  pickedList: [], // 已拣列表
  pickedNum: 0, // 已拣数量
  pickedDetailInfo: {}, // 已拣明细
  initLoading: false, // 初始化loading
  listData: [], // 领取任务-任务列表
  activeNavKey: 1, // 明细页面-默认展示未拣数据
  activeHasPickKeys: [], // 点击已拣列表的key
  activeNoPickKeys: [], // 点击未拣列表的key
  resultType: '1', // 1:初始页面 2:执行任务页面 3:明细页面 4:换箱页面
  location: '', // 库位号
  locationDisabled: 1, // 扫描库位号disabled
  locationCantInput: false, // 不能输入库位号，跳转到条码输入框那里就不能输入库位号了
  goodsBarcode: '', // 条码
  newPickContainer: '', // 新的周转箱-用于换箱操作
  newPickContainerDisabled: 1, // 扫描新的周转箱disabled
  nextPickInfo: {}, // 用于复核成功后，更新拣货数据
  changePickInfo: {}, // 换箱请求回来的数据
  showTotal: false, // 是否展开 展示总数
};

export default {
  state: defaultState,

  $init: () => defaultState,

  * init() {
    yield this.changeData({
      initLoading: false,
      headerTitle: getHeaderTitle(),
    });
    // 初始化-获取拣货中的周转箱
    yield this.getPickingBox();
  },

  changeData(state, data) {
    assign(state, data);
  },
  /**
   * 获取拣货中的周转箱
   */
  * getPickingBox() {
    const result = yield getPickingBoxAPI();
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      yield this.changeData({
        pickingList: info.pickContainers,
      });
      classFocus('pickContainerCode');
    } else {
      modal.error({ content: msg, className: 'pickContainerCode' });
    }
  },
  /**
   * 扫描拣货周转箱-领取拣货任务
   * @param {string} pickContainerCode 拣货周转箱
   */
  * scanContainer(pickContainerCode) {
    markStatus('pickContainerDisabled');
    const result = yield takePickTaskAPI({ pickContainerCode });
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      // type 1/继续绑定箱子，2/跳转到拣货页面拣货(执行任务页面)，3/弹出其他任务都已经取消的提示窗, 4/任务结束，5/任务取消
      const { type, taskCode, changeAreaInfo } = info;
      // changeAreaInfo不为空代表已经进行换区
      if (changeAreaInfo) {
        const {
          area,
          floor,
          lastArea,
          lastFloor,
        } = changeAreaInfo;
        // 换区提示框
        const status = yield new Promise((r) => modal.info({
          content: (
            // eslint-disable-next-line react/jsx-filename-extension
            <div>
              {t('本任务将由上一个任务的')}
              <span>{lastFloor}</span>
              {t('层')}
              <span style={{ color: 'red' }}>{lastArea}</span>
              {t('区')}{t('切换为')}
              <span>{floor}</span>
              {t('层')}
              <span style={{ color: 'red', fontSize: 18 }}>{area}</span>
              {t('区')},{t('请注意')}！
            </div>
          ),
          onOk: () => r('ok'),
        }));
        // 继续绑定箱子
        if (status === 'ok') {
          yield this.changeData({
            pickContainerCode: '',
          });
          yield this.getPickingBox();
          return;
        }
        return;
      }
      // 继续绑定箱子
      if (type === 1) {
        yield this.changeData({
          pickContainerCode: '',
        });
        yield this.getPickingBox();
        return;
      }
      // 跳转到拣货页面拣货(执行任务页面)
      if (type === 2) {
        yield this.getRecommendInfo({ pickContainerCode, taskCode });
        yield this.changeData({
          resultType: '2',
          showTotal: false,
        });
        return;
      }
      // 弹出其他任务都已经取消的提示窗
      if (type === 3) {
        const flag = yield new Promise((r) => modal.info({
          content: t('组合任务中有一个任务被取消，确认继续拣货'),
          onOk: () => r('ok'),
        }));
        if (flag === 'ok') {
          yield this.getRecommendInfo({ pickContainerCode, taskCode });
          yield this.changeData({
            resultType: '2',
            showTotal: false,
          });
          classFocus('pickContainerCode');
        }
      }
    } else {
      yield this.changeData({
        pickContainerCode: '',
      });
      modal.error({ content: msg, className: 'pickContainerCode' });
    }
  },
  /**
   * 获取拣货中的任务
   * type === 'init-page'表示：在领取任务页面，点击【继续拣货】按钮
   */
  * getPickingTask(type) {
    const result = yield getPickingTaskAPI();
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      if ([1].includes(info.popUpWindowFlag) && info.tips && info.tips.length === 0) {
        yield this.init();
        return;
      }
      // popUpWindowFlag "弹窗标识：1：组合内所有任务都已完成, 3：任务全部取消"
      if ([1, 3].includes(info.popUpWindowFlag)) {
        yield this.showEndModalNew({ info, eventName: 'getPickingTask' });
        return;
      }
      yield this.updateNextPickData(info);
      if (type === 'init-page') {
        // 跳转到执行页面
        yield this.changeData({
          resultType: '2',
          showTotal: false,
        });
      }
    } else {
      modal.error({ content: msg });
    }
  },
  /**
   * 更新下一条拣货数据
   */
  * updateNextPickData(info) {
    // 根据这个taskOrder字段来判断它是1号还是2号，语音提示“1号/2号”
    const { taskOrder } = info;
    if (taskOrder === 1) {
      audio('putOn1', 'play');
    }
    if (taskOrder === 2) {
      audio('putOn2', 'play');
    }
    yield this.changeData({
      recommendInfo: info,
      pickContainerCode: info.pickContainerCode,
      goodsBarcode: '',
      location: '',
      locationCantInput: false,
    });
  },
  /**
   * 获取任务明细推荐
   * @param {string} pickContainerCode 拣货周转箱
   * @param {string} taskCode 任务号
   */
  * getRecommendInfo({ pickContainerCode, taskCode }) {
    const result = yield taskDetailRecommendAPI({ pickContainerCode, taskCode });
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      yield this.updateNextPickData(info);
    } else {
      yield this.changeData({
        pickContainerCode: '',
      });
      modal.error({ content: msg, className: 'pickContainerCode' });
    }
  },
  /**
   * 查询未拣选列表
   * @param {string} groupCode 多任务组合号
   */
  * getWaitPick(groupCode) {
    const result = yield listWaitPickAPI({
      groupCode,
    });
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      yield this.changeData({
        waitPickList: info.data,
        waitPickedNum: info.totalNum, // 未拣数量
      });
    } else {
      modal.error({ content: msg });
    }
  },
  /**
    * 查询未拣货周转箱详情
    * @param {string} taskCode 任务号
   */
  * getWaitPickDetail(taskCode) {
    const { waitPickDetailInfo } = yield '';
    const result = yield waitPickDetailAPI({
      taskCode,
    });
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      yield this.changeData({
        waitPickDetailInfo: {
          ...waitPickDetailInfo,
          [taskCode]: info,
        },
      });
    } else {
      modal.error({ content: msg });
    }
  },
  /**
   * 查询已拣选列表
   * @param {string} groupCode 多任务组合号
   */
  * getPickList(groupCode) {
    const result = yield listPickedAPI({
      groupCode,
    });
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      yield this.changeData({
        pickedList: info.data,
        pickedNum: info.totalNum,
      });
    } else {
      modal.error({ content: msg });
    }
  },
  /**
    * 查询已拣货周转箱详情
    * @param {string} pickContainerId 拣货周转箱
    * @param {string} taskCode 任务号
   */
  * getPickedDetail({ pickContainerId, taskCode }) {
    const { pickedDetailInfo } = yield '';
    const result = yield pickedDetailAPI({
      pickContainerId,
      taskCode,
    });
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      yield this.changeData({
        pickedDetailInfo: {
          ...pickedDetailInfo,
          [taskCode]: info,
        },
      });
    } else {
      modal.error({ content: msg });
    }
  },
  /**
   * 扫描拣货库位
   */
  * scanLocation(location) {
    markStatus('locationDisabled');
    const { recommendInfo } = yield '';
    const { taskCode, taskDetailId } = recommendInfo;
    const result = yield scanLocationAPI({
      location,
      taskCode,
      taskDetailId,
    });
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      const { popUpWindowFlag } = info;
      // pop_up_window_flag ：1：组合内所有任务都已完成,2：组合中本任务已完成, 3-提示扫描正确库位,4:任务已取消 5：提示   *号任务结束， 重新推荐扫描
      if (popUpWindowFlag === 3) {
        modal.error({ title: t('请扫描需拣货的正确库位'), className: 'location' });
        yield this.changeData({ location: '' });
        return;
      }
      if ([1, 2, 4, 5].includes(popUpWindowFlag)) {
        yield this.showEndModalNew({ info, eventName: 'scanLocation' });
        return;
      }
      // 库位号输入框禁用 焦点置于商品条码
      yield this.changeData({
        locationCantInput: true,
        goodsBarcodeDisabledFlag: false,
      });
      classFocus('goodsBarcode');
    } else {
      yield this.changeData({
        location: '',
      });
      modal.error({ content: msg, className: 'location' });
    }
  },
  /**
   * 跳过拣货库位
   */
  * skipLocation() {
    markStatus('locationDisabled');
    const { recommendInfo } = yield '';
    const {
      goodsSn, location, size, skuCode, taskCode,
    } = recommendInfo;
    const result = yield takeSkipLocationAPI({
      goodsSn, location, size, skuCode, taskCode,
    });
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      const { popUpWindowFlag } = info;
      // popUpWindowFlag 1：组合内所有任务都已完成,2：组合中本任务已完成， 3：任务已取消 4：提示   *号任务结束， 重新推荐扫描
      if ([1, 2, 3, 4].includes(popUpWindowFlag)) {
        yield this.showEndModalNew({ info, eventName: 'skipLocation' });
        return;
      }
      // 获取到新的任务明细，清空库位，光标定位扫描库位，条码
      yield this.getPickingTask();
      yield this.changeData({
        location: '',
        goodsBarcode: '',
      });
      classFocus('location');
    } else {
      yield this.changeData({
        location: '',
      });
      modal.error({ content: msg, className: 'location' });
    }
  },

  /**
   * 扫描条码
   * @param {string} goodsBarcode 条码
   */
  * scanGoodsBarcode(goodsBarcode) {
    const { recommendInfo, pickContainerCode } = yield '';
    const {
      location, taskCode, skuCode,
    } = recommendInfo;
    const result = yield postPickDataAPI({
      goodsBarcode,
      location,
      taskCode,
      pickContainerCode,
    });
    const {
      msg, code, info,
    } = result;
    if (code === '0') {
      const { popUpWindowFlag, checkContainer, nextPick } = info;
      const {
        skuCode: nextSkuCode,
        location: nextLocation,
        taskCode: nextTaskCode,
      } = nextPick || {};

      // 复核
      if (checkContainer && !nextPick) {
        // 复核 焦点置于周转箱输入框 清空周转箱、库位号、条形码
        yield this.changeData({
          location: '',
          pickContainerCode: '',
          goodsBarcode: '',
          locationCantInput: false,
          nextPickInfo: nextPick,
          pickContainerDisabledFlag: false,
          goodsBarcodeDisabledFlag: true,
        });
        classFocus('pickContainerCode');
        return;
      }

      // 开启复核周转箱 任务号 + 库位号不同，进行复核周转箱
      if (checkContainer && nextPick && !(location === nextLocation && taskCode === nextTaskCode)) {
        // 复核 焦点置于周转箱输入框 清空周转箱、库位号、条形码
        yield this.changeData({
          location: '',
          pickContainerCode: '',
          goodsBarcode: '',
          locationCantInput: false,
          nextPickInfo: nextPick,
          pickContainerDisabledFlag: false,
          goodsBarcodeDisabledFlag: true,
        });
        classFocus('pickContainerCode');
        return;
      }
      // popUpWindowFlag 1：组合内所有任务都已完成,2：组合中本任务已完成, 3:任务已取消 4：提示   *号任务结束， 重新推荐扫描
      if ([1, 2, 3, 4].includes(popUpWindowFlag)) {
        yield this.showEndModalNew({ info, eventName: 'scanGoodsBarcode' });
        return;
      }
      // 任务号+库位号+SKU号不相同，清空库位，条码，焦点聚焦到库位
      if (
        nextPick &&
        !(skuCode === nextSkuCode && location === nextLocation && taskCode === nextTaskCode)) {
        // 下一个推荐任务
        yield this.updateNextPickData(nextPick);
        yield this.changeData({
          location: '',
          goodsBarcode: '',
          locationCantInput: false,
          goodsBarcodeDisabledFlag: true,
        });
        classFocus('location');
        return;
      }

      // 下一个推荐任务
      if (nextPick) {
        // 根据这个taskOrder字段来判断它是1号还是2号，语音提示“1号/2号”
        const { taskOrder } = nextPick;
        if (taskOrder === 1) {
          audio('putOn1', 'play');
        }
        if (taskOrder === 2) {
          audio('putOn2', 'play');
        }
        yield this.changeData({
          recommendInfo: nextPick,
          pickContainerCode: nextPick.pickContainerCode,
        });
      }
      // 扫描商品条码
      yield this.changeData({
        goodsBarcode: '',
        goodsBarcodeDisabledFlag: false,
      });
      classFocus('goodsBarcode');
    } else {
      yield this.changeData({
        goodsBarcode: '',
      });
      modal.error({ content: msg, className: 'goodsBarcode' });
    }
  },
  /**
   * 扫描周转箱复核
   */
  * pickContainerCheck(pickContainerCode) {
    markStatus('pickContainerDisabled');
    const { recommendInfo, nextPickInfo } = yield '';
    const { taskCode } = recommendInfo;
    const result = yield pickContainerCheckAPI({
      pickContainerCode,
      taskCode,
    });

    if (result.code === '0') {
      const { info } = result;
      const { popUpWindowFlag } = info;
      if (popUpWindowFlag === 1 || popUpWindowFlag === 2) {
        yield this.showEndModalNew({ info, eventName: 'pickContainerCheck' });
      }
      // 复核成功，获取下一个任务
      yield this.updateNextPickData(nextPickInfo);
      yield this.changeData({
        goodsBarcode: '',
        location: '',
        locationCantInput: false,
        pickContainerDisabledFlag: true,
      });
      classFocus('location');
    } else {
      yield this.changeData({
        pickContainerCode: '',
      });
      modal.error({
        title: result.msg,
        className: 'pickContainerCode',
      });
    }
  },
  /**
   * PDA拣货短拣
   * @returns {IterableIterator<*>}
   */
  * shortPick() {
    const { recommendInfo, pickContainerCode } = yield '';

    const {
      location,
      pickNum,
      // pickSeq,
      // pickType,
      taskCode,
      taskDetailId,
      locationHadPickNum,
    } = recommendInfo;
    const result = yield shortPickAPI({
      location,
      pickContainerCode,
      pickExceptionNum: pickNum - locationHadPickNum, // 异常件数
      // pickSeq, // 异常件数
      // pickType, // 拣货类型
      taskCode,
      taskDetailId, // 任务明细ID
    });
    const { code, info, msg } = result;
    if (code === '0') {
      const {
        pdaTakePickTaskRsp, popUpWindowFlag,
      } = info;
      // popUpWindowFlag 1：组合内所有任务都已完成,2：组合中本任务已完成, 3:任务已取消 4：提示   *号任务结束， 重新推荐扫描
      if ([1, 2, 3, 4].includes(popUpWindowFlag)) {
        yield this.showEndModalNew({ info, eventName: 'shortPick' });
        return;
      }
      // 如果不是最后一件则返回下一件的数据，否则为null ,返回任务领取页面
      if (pdaTakePickTaskRsp === null) {
        message.success(t('短拣成功'));
        yield this.init();
      } else {
        yield this.updateNextPickData(pdaTakePickTaskRsp);
        yield this.changeData({
          goodsBarcode: '',
          location: '',
          locationCantInput: false,
          goodsBarcodeDisabledFlag: true,
          nextPick: pdaTakePickTaskRsp,
        });
        classFocus('location');
      }
    } else {
      modal.error({ content: msg, className: 'location' });
    }
  },
  /**
   * 换箱操作
   * @param {string} changeStage 换箱阶段 0-点击换箱按钮 1-实际换箱
   */
  * changePickContainer(changeStage) {
    markStatus('newPickContainerDisabled');
    const { newPickContainerCode, recommendInfo, pickContainerCode } = yield '';
    const { taskCode } = recommendInfo;
    const hasChangeCodeObj = changeStage === 1 ?
      { changePickContainerCode: newPickContainerCode }
      : {}; // 需要更换的拣货周转箱号，1-实际换箱时传入
    const result = yield changePickContainerAPI({
      changeStage,
      pickContainerCode,
      taskCode,
      warehouseId: (JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE)) || {}).warehouseId,
      ...hasChangeCodeObj,
    });

    if (result.code === '0') {
      if (changeStage === 0) {
      // 跳转到换箱页面
        yield this.changeData({
          resultType: '4',
          newPickContainerCode: '',
          changePickInfo: result.info,
        });
        classFocus('newPickContainerCode');
      } else {
      // 返回执行页面
        yield this.changeData({
          resultType: '2',
          pickContainerCode: newPickContainerCode,
        });
      }
    } else {
      message.error(result.msg);
      classFocus('newPickContainerCode');
    }
  },
  /**
   * 弹出任务完成提示框
   * @param {object} info 请求的返参
   * @param {string} eventName 事件名
   */
  * showEndModalNew({ info, eventName }) {
    console.log('eventName', eventName);
    const { tips } = info;
    // 扫描条码 短拣 跳过库位 获取拣货任务 popUpWindowFlag === 3 表示任务已取消
    const cancelFlag = ['scanGoodsBarcode', 'shortPick', 'skipLocation', 'getPickingTask'].includes(eventName) && info.popUpWindowFlag === 3;
    // 扫描库位 popUpWindowFlag === 3 表示任务已取消
    const locationCancelFlag = ['scanLocation'].includes(eventName) && info.popUpWindowFlag === 4;

    if (cancelFlag || locationCancelFlag) {
      audio('error', 'play');
      const flag = yield new Promise((r) => modal.info({
        content: t('任务已取消，请扫描周转箱重新领取任务'),
        onOk: () => r('ok'),
      }));
      if (flag === 'ok') {
        yield this.init();
      }
      return;
    }
    // 扫描库位(5)/扫描商品条码(4)/跳过库位(4)/短拣(4)， 提示   *号任务结束， 重新推荐扫描
    const recommendFlag = ['scanGoodsBarcode', 'shortPick', 'skipLocation'].includes(eventName) && info.popUpWindowFlag === 4;
    const locationRecommendFlag = ['scanLocation'].includes(eventName) && info.popUpWindowFlag === 5;
    if (recommendFlag || locationRecommendFlag) {
      const { recommendInfo } = yield '';
      const { taskOrder } = recommendInfo;
      const flag = yield new Promise((r) => modal.info({
        content: t('{}号任务结束，重新推荐扫描', taskOrder),
        onOk: () => r('ok'),
      }));
      if (flag === 'ok') {
        yield this.getPickingTask();
      }
      return;
    }

    const status = yield new Promise((r) => modal.info({
      content: (
        tips?.map((item, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <div key={index}>
            <div className={styles.tipsBlock}>
              <div>
                <span className={classnames(styles.tipsNum, styles[item.order === 1 ? 'numberOne' : 'numberTwo'])}>
                  {t('{}号', item.order)}
                </span>
                {item.pickContainerCode ? t('任务拣货完成，周转箱{}请投线！', item.pickContainerCode) : t('任务拣货完成')}
              </div>
              {item.pickContainerCode && item.collectTips && <div>{t('集货提示')}：{item.collectTips}</div>}
              {item.pickContainerCode && item.wellenTips && <div>{t('波次提示')}：{item.wellenTips}</div>}
              <div>
                {item.pickContainerCode && item.nationLineName &&
                <span className={styles.nationalLine}>{item.nationLineName}</span>}
              </div>
            </div>
          </div>
        ))
      ),
      onOk: () => {
        r('ok');
      },
    }));
    if (status === 'ok') {
      // popUpWindowFlag 1：组合内所有任务都已完成,2：组合中本任务已完成
      // popUpWindowFlag === 1 任务完成： 初始化页面
      if (info.popUpWindowFlag === 1) {
        yield this.init();
        return;
      }
      // popUpWindowFlag === 2 本任务完成：  清空库位，聚焦库位
      if (info.popUpWindowFlag === 2) {
        const nextPick = yield '';
        // 【扫描条码、复核】不请求推荐任务，取扫描条码接口返回的nextpick
        if (['scanGoodsBarcode', 'pickContainerCheck'].includes(eventName)) {
          // const data = info.pdaTakePickTaskRsp || info.nextPick;
          let data = {};
          switch (eventName) {
            case 'scanGoodsBarcode':
              data = info.nextPick;
              break;
            default:
              data = nextPick;
              break;
          }
          yield this.updateNextPickData(data);
        } else {
          // 【扫描库位、短拣完】本任务完成的话再调用wos/front/multi/picking_task领取新的任务去扫
          yield this.getPickingTask();
        }
        yield this.changeData({
          goodsBarcode: '',
          location: '',
          locationCantInput: false,
          goodsBarcodeDisabledFlag: true,
        });
        classFocus('location');
      }
    }
  },
  /**
   * 获取图片
   */
  * getImage(row) {
    const { size, skcCode } = row;
    const result = yield getImageAPI({
      size,
      skcCode,
    });

    if (result.code === '0') {
      const img = <img width="100%" src={result.info} />;
      modal.img({
        content: img,
        // eslint-disable-next-line jsx-a11y/anchor-is-valid
        cancelText: <a className={styles.dichotomyStationA}>{t('确定')}</a>,
      });
    } else {
      message.error(result.msg);
      classFocus('newPickContainerCode');
    }
  },
};
