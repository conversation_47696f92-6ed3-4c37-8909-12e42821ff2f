
.topBlock{
  padding: 0px 15px 8px 30px;
}
.topBottom{
    display: flex;
    justify-content: space-between;
}
.recommendLocation{
  font-size: 18px;
  font-weight: normal;
}
.dichotomyStationA{
    color: #0059ce;
}
.taskTitle{
  font-weight: 600;
  font-size: 20px;
  color: rgb(153, 157, 168);
  line-height: 50px;
  text-align: center;
}
.taskPannel{
  height: calc(100vh - 110px);
  overflow: auto;
}
.pickingListPannel{
  height: calc(100vh - 160px);
  overflow: auto;
}
.itemStyle{
  border: 1px solid #f4f5f8;
  font-size: 13px;
  color: #616161;
  margin: 5px 10px;
}

.listItem{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #f4f5f8;
  font-size: 12px;
  color: #616161;
  margin: 5px 10px;
  padding: 8px 15px;
  box-shadow: 0px 2px 4px 0px rgb(25 122 250 / 15%);
  background-color: #fff;
}

.listIcon{
  // color: #616161;
  position: relative;
  margin-left: 13px;
  font-weight: 600;
  font-size: 14px;
  text-align: left;
  line-height: 28px;
  color: rgb(51, 62, 89);
}
.itemStatus{
  margin-right: 10px;
}

.listIcon:before {
  content: "";
  width: 4px;
  height: 14px;
  box-shadow: 0px 2px 4px 0px rgba(54,210,120,0.3);
  border-radius: 2px;
  position: absolute;
  top: 8px;
  left: -12px;
}

.listIcon.numberOne:before {
  background: #0059ce;
}

.listIcon.numberTwo:before {
  background: #52c41a;
}

.listNum{
  font-size: 28px;
  font-weight: 600;
}

.listNum.numberOne {
  color: #0059ce;
}
.listNum.numberTwo {
  color:#52c41a;
}

// perform-task-page
.firstItem{
  // display: inline-flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  // height: 36px;
  // line-height: 34px;
  padding: 0 10px;
  font-size: 13px;
  box-sizing: border-box;
  background: white;
  border-bottom: 1px solid #e5e5e5;
}
.topOne{
  height: 30px;
}
.topOne,.topTwo{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.nationalLine {
  display: inline-block;
  padding: 2px 6px;
  margin: 2px 0px;
  color: #ff9636;
  border: 1px solid #ff9636;
  height: 16px;
  line-height: 16px;
  font-size: 14px;
}

.totalRank {
  font-size: 16px;
  font-weight: 700;
}

.pickNum{
  color: #ff9636
}
.locationStyle{
  color: #141737;
  font-size: 20px;
  font-weight: 700;
}
.secondItem{
  background: white;
  padding: 5px 10px;
  border-bottom: 1px solid #e5e5e5;
  font-size: 13px;
}
.seconedline{
  line-height: 30px;
}
.secondBottom{
  display: flex;
  justify-content: space-between;
}
.totalRankText{
  font-size: 12px;
}
.secondCenter{
  color: #ff4d50;
}
.secondBottomLeft{
  color: #ff4d50;
  font-size: 15px;
}
.secondBottomRight{
  margin-right: 10px;
}
.thirdItem{
  display: flex;
  justify-content: flex-start;
  line-height: 35px;
  background: white;
  font-size: 14px;
  box-shadow: 0px 2px 4px 0px rgb(25 122 250 / 15%);
}
.locationWeight{
  font-weight: 600;
}
.flexOne{
  flex-basis: 33.3%;
  text-align: center;
}
.lackGoodsBtnZh {
  padding-left: 2px;
  padding-right: 2px;
  font-size: 10px;
  width: 48px !important;
  margin-bottom: 3px;
}
.lackGoodsBtnEn{
  padding-left: 2px;
  padding-right: 2px;
  font-size: 10px;
  width: 70px !important;
  margin-bottom: 3px;
}
.arrowBlock{
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.arrowItem{
  height: 30px;
  width: 95%;
  color: white;
  border-radius: 1px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-items: center;
}
.numberOneBackground{
  background: #0059ce;
}
.numberTwoBackground{
  background: #52c41a;
}
.footerBtnGreenClass{
  color: #52c41a;
  border-color: #52c41a;
}

.arrowItemInterBorder{
  border: 1px white dashed;
  flex: 1;
  margin: 2px;
}
.arrowStyle{
  text-align: center;
  flex: 1;
  background-size: 100%;
  height: 50px;
  color: white;
  background-color: #f4f5f8 !important;
  font-size: 28px;
}
.oneBackground{
  background: url('./image/blue.svg') no-repeat center center;
}
.twoBackground{
  background: url('./image/green.svg') no-repeat center center;
}
// detail-page


.detailListItem{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #f4f5f8;
  font-size: 12px;
  color: #616161;
  margin: 5px 0px;
  padding: 8px 15px;
  box-shadow: 0px 2px 4px 0px rgb(25 122 250 / 15%);
  background-color: #fff;
}
.detaiNoPicklListItem{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #f4f5f8;
  font-size: 12px;
  color: #616161;
  margin: 5px 0px;
  padding: 8px 15px;
  box-shadow: 0px 2px 4px 0px rgb(25 122 250 / 15%);
  background-color: #fff;
  height: 35px;
}
.detailListIcon{
  position: relative;
  margin-left: 13px;
  font-weight: 500;
  font-size: 13px;
  text-align: left;
  line-height: 25px;
}

.detailListIcon:before {
  content: "";
  width: 4px;
  height: 18px;
  box-shadow: 0px 2px 4px 0px rgba(54,210,120,0.3);
  border-radius: 2px;
  position: absolute;
  top: 18px;
  left: -14px;
}

.detailListIcon.numberOne:before {
  background: #0059ce;
}

.detailListIcon.numberTwo:before {
  background: #52c41a;
}

.detailNoPickListIcon{
  position: relative;
  margin-left: 13px;
  font-weight: 500;
  font-size: 13px;
  text-align: left;
  line-height: 25px;
}

.detailNoPickListIcon:before {
  content: "";
  width: 4px;
  height: 14px;
  box-shadow: 0px 2px 4px 0px rgba(54,210,120,0.3);
  border-radius: 2px;
  position: absolute;
  top: 5px;
  left: -14px;
}

.detailNoPickListIcon.numberOne:before {
  background: #0059ce;
}

.detailNoPickListIcon.numberTwo:before {
  background: #52c41a;
}

.detailListNum{
  font-size: 28px;
  font-weight: 600;
  font-style:italic;
}

.detailListNum.numberOne {
  color: #0059ce;
}
.detailListNum.numberTwo {
  color:#52c41a;
}
.numText{
  margin-right: 15px;
}
.showArrow {
  width: 16px;
  height: 16px;
  // position: absolute;
  // right: 0px;
  // bottom: 8px;
  color: #999999;
  font-size: 18px;
}

.noPickLeftTitle{
  text-align: left;
  margin-left: 15px;
}

.tipsBlock{
  font-size: 13px;
  line-height: 25px;
  margin-bottom: 10px;
}
.tipsNum{
  font-size: 20px;
  font-weight: 600;
  font-style: italic;
}

.tipsNum.numberOne {
  color: #0059ce;
}
.tipsNum.numberTwo {
  color:#52c41a;
}
// change-box-page
.tipsBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 8px;
  background-color: #d7e3f8;
}
.midddleText{
  text-align: center;
  line-height: 30px;
  font-size: 14px;
  margin: 20px 0px;
}

.leftAlign{
  text-align: left;
  margin-left: 10px;
}