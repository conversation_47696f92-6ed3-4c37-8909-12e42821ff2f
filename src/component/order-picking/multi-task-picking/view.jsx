import { t, i18n } from '@shein-bbl/react';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Header, pages,
} from 'common';
import {
  classFocus,
} from 'lib/util';
import store from './reducers';
import Modal from '../../common/modal';
import InitPage from './jsx/init-page';
import PerformTaskPage from './jsx/perform-task-page';
import DetailPage from './jsx/detail-page';
import ChangeBoxPage from './jsx/change-box-page';

const { View } = pages;
class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      initLoading,
      resultType,
      recommendInfo,
      location,
    } = this.props;
    // 执行任务页面-如果为2号任务则显示绿色背景，否则为默认颜色
    const greenBackground = (resultType === '2' && recommendInfo.taskOrder === 2) ? '#52c41a' : null;
    let ComponentPage = '';
    let title = headerTitle || t('多任务拣货');
    // resultType 1:初始页面 2:执行任务页面 3:明细页面 4:换箱页面
    switch (resultType) {
      case '1':
        ComponentPage = <InitPage {...this.props} />;
        break;
      case '2':
        ComponentPage = <PerformTaskPage {...this.props} />;
        break;
      case '3':
        title = t('拣货明细');
        ComponentPage = <DetailPage {...this.props} />;
        break;
      case '4':
        ComponentPage = <ChangeBoxPage {...this.props} />;
        break;
      default:
        ComponentPage = <InitPage {...this.props} />;
        break;
    }
    return (
      <div>
        <Header title={title} style={{ width: '100%' }} headerBackground={greenBackground}>
          <div>
            {/* 执行页面展示短拣按钮 */}
            {resultType === '2' ? (
              <div onClick={() => {
                if (!location) {
                  return;
                }
                if (location) {
                  if (location !== recommendInfo.location) {
                    Modal.error({ title: t('请扫描需拣货的正确库位') });
                    store.changeData({ location: '' });
                    classFocus('location');
                    return;
                  }
                }
                Modal.confirm({
                  content: t('确认短拣?'),
                  onOk: () => store.shortPick(),
                  onCancel: () => {
                    classFocus('location');
                  },
                });
              }}
              >{t('短拣')}
              </div>
            ) : null}
          </div>
        </Header>
        <View
          flex={false} // flex布局，默认为true，当需要固定单个输入框是，不启用
          diff={54} // 页面高度：window.innerHeight - diff 中的 diff 值
          initLoading={initLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面
        >
          {ComponentPage}
        </View>
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  totalContainerNum: PropTypes.number,
  totalPalletNum: PropTypes.number,
  initLoading: PropTypes.bool,
  scanBool: PropTypes.bool,
  resultType: PropTypes.string,
  recommendInfo: PropTypes.shape(),
  location: PropTypes.string,
};

export default i18n(Container);
