import { sendPostRequest } from '../../../lib/public-request';

/**
 * 获取拣货中的周转箱
 * @param {object} param
 * @returns
 */
export const getPickingBoxAPI = (param) => sendPostRequest({
  url: '/multi/picking_container',
  param,
}, process.env.WOS_URI);

/**
 * 领取拣货任务
 * @param {object} param
 * @returns
 */
export const takePickTaskAPI = (param) => sendPostRequest({
  url: '/multi/take_pick_task',
  param,
}, process.env.WOS_URI);

/**
 * 查询已拣选列表
 * @param {object} param
 * @returns
 */
export const listPickedAPI = (param) => sendPostRequest({
  url: '/multi/list_picked',
  param,
}, process.env.WOS_URI);

/**
 * 查询未拣选列表
 * @param {object} param
 * @returns
 */
export const listWaitPickAPI = (param) => sendPostRequest({
  url: '/multi/list_wait_pick',
  param,
}, process.env.WOS_URI);

/**
 * (2-PDA拣货)提交拣货数据
 * @param {object} param
 * @returns
 */
export const postPickDataAPI = (param) => sendPostRequest({
  url: '/multi/post_pick_data',
  param,
}, process.env.WOS_URI);

/**
 * (2-PDA拣货)短拣
 * @param {object} param
 * @returns
 */
export const shortPickAPI = (param) => sendPostRequest({
  url: '/multi/short_pick',
  param,
}, process.env.WOS_URI);

/**
 * 查询未拣货周转箱详情
 * @param {object} param
 * @returns
 */
export const waitPickDetailAPI = (param) => sendPostRequest({
  url: '/multi/wait_pick_detail',
  param,
}, process.env.WOS_URI);

/**
 * 查询已拣货周转箱详情
 * @param {object} param
 * @returns
 */
export const pickedDetailAPI = (param) => sendPostRequest({
  url: '/multi/picked_detail',
  param,
}, process.env.WOS_URI);

/**
 * 任务明细推荐
 * @param {object} param
 * @returns
 */
export const taskDetailRecommendAPI = (param) => sendPostRequest({
  url: '/multi/task_detail_recommend',
  param,
}, process.env.WOS_URI);

/**
 * 拣货周转箱换箱
 * @param {object} param
 * @returns
 */
export const changePickContainerAPI = (param) => sendPostRequest({
  url: '/multi/change_pick_container',
  param,
}, process.env.WOS_URI);

/**
 * 扫描周转箱复核
 * @param {object} param
 * @returns
 */
export const pickContainerCheckAPI = (param) => sendPostRequest({
  url: '/multi/check_pick_container',
  param,
}, process.env.WOS_URI);

/**
 * 跳过拣货库位
 * @param {object} param
 * @returns
 */
export const takeSkipLocationAPI = (param) => sendPostRequest({
  url: '/multi/take_skip_location',
  param,
}, process.env.WOS_URI);

/**
 * 扫描库位
 * @param {object} param
 * @returns
 */
export const scanLocationAPI = (param) => sendPostRequest({
  url: '/multi/scan_location',
  param,
}, process.env.WOS_URI);

/**
 * 获取图片
 * @param {object} param
 * @returns
 */
export const getImageAPI = (param) => sendPostRequest({
  url: '/multi/goods_image',
  param,
}, process.env.WOS_URI);

/**
 * 继续进行拣货中的任务
 * @param {object} param
 * @returns
 */
export const getPickingTaskAPI = (param) => sendPostRequest({
  url: '/multi/picking_task',
  param,
}, process.env.WOS_URI);
