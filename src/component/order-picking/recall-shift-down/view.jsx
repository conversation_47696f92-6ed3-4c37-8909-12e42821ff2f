import React, { Component } from 'react';
import { i18n, t } from '@shein-bbl/react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import ScanContainer from './jsx/scan-container';
import DetailList from './jsx/detail';
import { Header, modal } from '../../common';
import store from './reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
  }
  render() {
    const {
      headerTitle,
      type,
      goodsCount,
      boxCode,
    } = this.props;
    return (
      <div>

        {type === 1 ? (
          <>
            <Header title={headerTitle || t('撤仓下架')}>
              <div
                onClick={() => {
                  if (goodsCount <= 0) {
                    modal.error({
                      title: t('无已装箱商品'),
                    });
                    return;
                  }
                  store.getGoodsDetail({
                    params: {
                      boxCode,
                    },
                  });
                }}
              >
                {t('明细')}
              </div>
            </Header>
            <ScanContainer {...this.props} />
          </>
        ) : (
          <>
            <Header title={headerTitle || t('已装箱商品明细')} />
            <DetailList {...this.props} />
          </>
        )}
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  type: PropTypes.number,
  goodsCount: PropTypes.number,
  boxCode: PropTypes.string,
};

const mapStateToProps = state => state['order-picking/recall-shift-down'];
export default connect(mapStateToProps)(i18n(Container));
