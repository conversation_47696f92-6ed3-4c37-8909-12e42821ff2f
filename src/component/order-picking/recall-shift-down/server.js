import { sendPostRequest } from '../../../lib/public-request';

/**
 * 页面初始化 查询当前用户的作业信息
 * @param param
 * @returns {*}
 */
export const pageInitApi = param => sendPostRequest({
  url: '/withdraw/get_user_info',
  param,
}, process.env.WOS_URI);


/**
 * 扫描大箱号
 * @param param
 * @returns {*}
 */
export const scanContainerApi = param => sendPostRequest({
  url: '/withdraw/scan_box_code',
  param,
}, process.env.WOS_URI);

/**
 *扫描库位
 * @param param
 * @returns {*}
 */
export const scanLocationApi = param => sendPostRequest({
  url: '/withdraw/scan_location',
  param,
}, process.env.WOS_URI);


/**
 * 扫描商品条码
 * @param param
 * @returns {*}
 */
export const scanGoodsApi = param => sendPostRequest({
  url: '/withdraw/scan_barcode',
  param,
}, process.env.WOS_URI);

/**
 * 关箱
 * @param param
 * @returns {*}
 */
export const closeContainerApi = param => sendPostRequest({
  url: '/withdraw/close_box',
  param,
}, process.env.WOS_URI);

/**
 * 移位下架-获取明细
 * @param param
 * @returns {*}
 */
export const getGoodsDetailApi = param => sendPostRequest({
  url: '/withdraw/get_detail_info',
  param,
}, process.env.WOS_URI);
