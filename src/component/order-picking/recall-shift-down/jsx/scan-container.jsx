import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import {
  Form,
  Button,
} from 'react-weui/build/packages';
import { push } from 'react-router-redux';
import Modal from 'common/modal';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import Footer from '../../../common/footer';
import RowInfo from '../../../common/row-info';
import { getUsername, getWarehouseId } from '../../../../lib/util';


class ScanContainer extends Component {

  render() {
    const {
      goodsCount,
      boxCode,
      location,
      isBoxCodeDisabled,
      isLocationDisabled,
      isBarCodeDisabled,
      isCloseDisabled,
      dispatch,
      loading,
    } = this.props;

    return (
      <div>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            disabled={isBoxCodeDisabled || !loading}
            autoFocus
            className="boxCode"
            data-bind="boxCode"
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.changeData({
                data: {
                  isBoxCodeDisabled: true,
                },
              });

              store.scanContainer({
                params: {
                  boxCode,
                },
              });
            }}
            footer={
              (
                <Button
                  style={{ marginBottom: 5 }}
                  disabled={isCloseDisabled}
                  size="small"
                  onClick={() => {
                    Modal.confirm({
                      content: t('是否确认关箱?'),
                      onOk: () => {
                        store.closeContainer({
                          params: {
                            boxCode,
                          },
                        });
                      },
                    });
                  }}
                >
                  {t('关箱')}
                </Button>
              )
            }
          >
            <label>{t('大箱号')}</label>
          </FocusInput>
        </Form>
        <Form>
          <RowInfo
            label={t('已装箱商品数量')}
            content={goodsCount}
          />
        </Form>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            disabled={isLocationDisabled}
            className="location"
            data-bind="location"
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.changeData({
                data: {
                  isLocationDisabled: true,
                },
              });
              store.scanLocation({
                params: {
                  location: e.target.value.replace(/^\s+/, ''),
                  userName: getUsername(),
                  warehouseId: getWarehouseId(),
                },
              });
            }}
            footer={
              (
                <Button
                  style={{ marginBottom: 5 }}
                  disabled={location === ''}
                  size="small"
                  onClick={() => {
                    store.changeData({
                      data: {
                        location: '',
                        isLocationDisabled: false,
                      },
                    });
                    store.classFocus('location');
                  }}
                >
                  {t('更换库位')}
                </Button>
              )
            }
          >
            <label>{t('库位号')}</label>
          </FocusInput>
        </Form>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            disabled={isBarCodeDisabled}
            className="barcode"
            data-bind="barcode"
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.changeData({
                data: {
                  isBarCodeDisabled: true,
                },
              });
              store.scanGoods({
                params: {
                  boxCode,
                  location: location.replace(/^\s+/, ''),
                  barcode: e.target.value.replace(/^\s+/, ''),
                  warehouseId: getWarehouseId(),
                  userName: getUsername(),
                },
              });
            }}
          >
            <label>{t('商品条码')}</label>
          </FocusInput>
        </Form>
        <Footer
          beforeBack={() => {
            store.init();
            dispatch(push('/order-picking'));
          }}
        />
      </div>
    );
  }
}

ScanContainer.propTypes = {
  isBoxCodeDisabled: PropTypes.bool,
  goodsCount: PropTypes.number,
  boxCode: PropTypes.string,
  location: PropTypes.string,
  isBarCodeDisabled: PropTypes.bool,
  isCloseDisabled: PropTypes.bool,
  isLocationDisabled: PropTypes.bool,
  dispatch: PropTypes.func,
  loading: PropTypes.number,
};

export default ScanContainer;
