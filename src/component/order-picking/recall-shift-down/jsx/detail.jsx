import React, { Component } from 'react';
import PropTypes from 'prop-types';
import store from '../reducers';
import Footer from '../../../common/footer';
import List from '../../../common/list';
import style from '../../../style.css';

const rows = [
  [
    {
      title: '',
      render: 'goodsSn',
    },
  ],
  [
    {
      title: '',
      render: 'goodsSize',
    },
  ],
];


class DetailList extends Component {
  render() {
    const {
      dispatch,
      detailList,
      focusPosition,
    } = this.props;

    const height = window.innerHeight - 44 - 56;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <List
          rows={rows}
          data={detailList}
        />
        <Footer
          dispatch={dispatch}
          beforeBack={() => {
            store.changeData({
              data: {
                type: 1,
              },
            });
            store.classFocus(focusPosition)
          }}
        />
      </div>
    );
  }
}

DetailList.propTypes = {
  dispatch: PropTypes.func,
  detailList: PropTypes.arrayOf(PropTypes.shape()),
  focusPosition: PropTypes.string,
};

export default DetailList;
