import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { t } from '@shein-bbl/react';
import { modal } from '../../common';
import {
  pageInitApi,
  scanLocationApi,
  scanGoodsApi,
  closeContainerApi,
  scanContainerApi,
  getGoodsDetailApi,
} from './server';
import { getHeaderTitle, getWarehouseId, classFocus as utilClassFocus } from '../../../lib/util';

const defaultState = {
  loading: 1,
  boxCode: '', // 箱号
  location: '', // 库位
  barcode: '', // 商品条码
  goodsCount: 0, // 已装箱商品数量
  headerTitle: '',
  isBoxCodeDisabled: false,
  isLocationDisabled: false,
  isCloseDisabled: true, // 关箱是否禁用
  detailList: [], // 明细
  type: 1, // 1 扫描页面 2 明细页面
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * classFocus(className, ctx) {
    utilClassFocus(className);
    yield ctx.changeData({
      data: {
        focusPosition: className,
      },
    });
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    markStatus('loading');
    const res = yield pageInitApi({ warehouseId: getWarehouseId() });
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          boxCode: res.info.boxCode,
          goodsCount: res.info.goodsCount || 0,
        },
      });
      if (res.info.boxCode) {
        yield ctx.changeData({
          data: {
            isBoxCodeDisabled: true, // 禁用扫箱号框
            isCloseDisabled: false, // 启用关闭箱号按钮
          },
        });
        yield ctx.classFocus('location');
      } else {
        yield ctx.classFocus('boxCode');
      }
    } else {
      modal.error({ content: res.msg, className: 'boxCode' });
    }
  },
  * scanContainer(action, ctx) {
    const data = yield scanContainerApi(action.params);
    if (data.code === '0') {
      const { boxCode, goodsCount } = data.info;
      yield ctx.changeData({
        data: {
          boxCode,
          goodsCount,
          isCloseDisabled: false,
        },
      });
      yield ctx.classFocus('location');
    } else {
      yield ctx.changeData({
        data: {
          boxCode: '',
          isBoxCodeDisabled: false,
        },
      });
      modal.error({ content: data.msg, className: 'boxCode' });
    }
  },
  * closeContainer(action, ctx) {
    const data = yield closeContainerApi(action.params);
    if (data.code === '0') {
      const status = yield new Promise((r) => modal.success({
        content: `${t('大箱')}${action.params.boxCode}${t('已成功关箱')}`,
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.classFocus('boxCode');
      }
      yield ctx.init();
    } else {
      modal.error({ content: data.msg });
    }
  },
  * scanLocation(action, ctx) {
    const data = yield scanLocationApi(action.params);
    if (data.code === '0') {
      yield ctx.classFocus('barcode');
    } else {
      yield ctx.changeData({ data: { location: '', isLocationDisabled: false } });
      modal.error({
        content: data.msg,
        className: 'location',
      });
    }
  },
  * scanGoods(action, ctx) {
    const data = yield scanGoodsApi(action.params);
    yield ctx.changeData({
      data: {
        isBarCodeDisabled: false,
      },
    });
    if (data.code === '0') {
      const { hasBoxedNum, isLast } = data.info;
      yield ctx.changeData({ data: { barcode: '', goodsCount: hasBoxedNum } });
      if (isLast) {
        yield ctx.changeData({ data: { location: '', isLocationDisabled: false } });
        yield ctx.classFocus('location');
      } else {
        yield ctx.classFocus('barcode');
      }
    } else {
      yield ctx.changeData({
        data: {
          barcode: '',
        },
      });
      modal.error({
        content: data.msg,
        className: 'barcode',
      });
    }
  },
  * getGoodsDetail(action, ctx) {
    const data = yield getGoodsDetailApi(action.params);
    if (data.code === '0') {
      yield ctx.changeData({ data: { detailList: data.info || [], type: 2 } });
    } else {
      console.log(data.msg);
    }
  },
};
