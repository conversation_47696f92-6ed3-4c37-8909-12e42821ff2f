.firstItem {
  display: inline-flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  height: 36px;
  line-height: 34px;
  padding: 0 10px;
  box-sizing: border-box;
}

.weight {
  font-size: 16px;
  font-weight: 700;
}

.mainInfo {
  width: 100%;
  border-bottom: 1px solid #ddd;
}

.mainItem {
  display: inline-flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  height: 30px;
  line-height: 28px;
  padding: 0 10px;
  box-sizing: border-box;
}

.sizeAndColorItem {
  justify-content: flex-end;
}

.mainItem>div {
  font-weight: 700;
}

.btn {
  width: 60px;
  line-height: 20px;
  background-color: #fff;
  border: 1px solid #FF3636;
  border-radius: 3px;
  color: #ff3636;
  font-size: 12px;
}

.btn1 {
  width: 40px;
  line-height: 20px;
  background-color: #1890ff;
  border: 1px solid #1890ff;
  border-radius: 3px;
  color: #fff;
  font-size: 12px;
}

.inputStyle {
  padding: 0 -1.6em;
}

.boxFloat{
  line-height: 36px;
  height: 36px;
}

.floatLeft{
  float: left;
  padding-left: 10px;
}

.floatRight{
  float: right;
  padding-right: 10px;
}

.imgCss {
  display: block;
  margin: 10px auto;
  max-width: 130px;
  max-height: 130px;
}
