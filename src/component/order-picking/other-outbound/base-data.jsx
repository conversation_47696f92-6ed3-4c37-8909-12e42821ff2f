import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages/components/form';
import RowInfo from '../../common/row-info';

const BaseData = (props) => {
  const {
    data,
    hideSky,
  } = props;
  return (
    <div>
      <Form>
        {
          data.map((item) => (
            item.type === 'sky' && hideSky ? null : (
              <RowInfo
                key={item.label}
                extraStyle={{
                  borderBottom: 'none',
                }}
                label={item.label}
                content={item.num}
                type={item.type}
              />
            )
          ))
        }
      </Form>
    </div>
  );
};

BaseData.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape()),
  hideSky: PropTypes.bool,
};

export default BaseData;
