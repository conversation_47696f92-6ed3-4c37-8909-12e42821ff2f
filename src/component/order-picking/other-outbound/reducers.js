import React from 'react';
import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { select } from 'redux-saga/effects';
import { t } from '@shein-bbl/react';
import {
  queryTask, pdaQueryRankInfo, scanPickContainer,
  closePickContainer, scanGoods, shortPick,
  getDetailApi,
  scanPPLocationAPI,
} from './server';
import Modal from '../../common/modal';
import { getApolloConfigAPI } from '../../../server/basic/common';
import { classFocus, getHeaderTitle, apolloFormatObj } from '../../../lib/util';
import message from '../../common/message';
import style from '../../style.css';

// 有未完成的任务时提示信息
const getTipsInfo = (entryIsAutoPickType, newIsAutoPickType) => {
  if (entryIsAutoPickType === newIsAutoPickType) {
    return newIsAutoPickType ? t('有未完成的任务') : t('有未关箱的任务');
  }
  return newIsAutoPickType ? t('自动化有未完成的任务，点击“是”继续拣货') : t('人工仓有未完成的任务，点击“是”继续拣货');
};

const defaultState = {
  status: 'initPage',
  isAutoStockPick: false, // 是否自动化库区
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  baseData: [
    {
      label: t('待领取任务数'),
      num: 0,
      type: 'info',
    },
    {
      label: t('已下架件数'),
      num: 0,
      type: 'warn',
    },
    {
      label: t('排名'),
      num: 0,
      type: 'sky',
    },
  ],
  containerCode: '', // 外层周转箱
  boxDisabled: 1,
  info: '',
  locationInput: '',
  containerDisabled: false,
  locationDisabled: 1,
  barCodeDisabled: 0,
  headerTitle: '',
  showDetail: false,
  detailList: [[], []],
  degradeScanUpgrade: false, // 降级开关
  openPicturePickingModel: false, // 是否打开图片拣货模式
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    yield ctx.getOpenPicturePickingModel();
  },
  // 左侧栏开启图片拣货模式
  * getOpenPicturePickingModel() {
    const {
      openPicturePickingModel,
    } = yield select((state) => state.nav);
    yield this.changeData({ data: { openPicturePickingModel } });
  },
  // 获取页面降级开关 DEGRADE_OTHER_OUTBOUND
  * getDegradeConfig(action, ctx) {
    try {
      const res = yield getApolloConfigAPI({ params: ['DEGRADE_OTHER_OUTBOUND'] });
      const apolloConfigFormatRes = apolloFormatObj(res.info);

      if (res.code === '0' && apolloConfigFormatRes?.DEGRADE_OTHER_OUTBOUND === '1') {
        yield ctx.changeData({ data: { degradeScanUpgrade: apolloConfigFormatRes?.DEGRADE_OTHER_OUTBOUND === '1' } });
      } else {
        // 获取排名。后端接口降级
        yield ctx.getRankInfo();
      }
    } catch (e) {
      console.error(e);
    }
  },
  * getInfo(action, ctx, put) {
    // 获取页面降级开关
    yield ctx.getDegradeConfig();

    markStatus('dataLoading');
    const res = yield queryTask();
    if (res.code === '0') {
      yield put((draft) => {
        draft.baseData[0].num = res.info.needOperateNum;
        draft.baseData[1].num = res.info.hadOperateNum;
        draft.boxDisabled = 1;
      });
      classFocus('box');
    } else {
      Modal.error({ content: res.msg || t('请求数据出错') });
    }
  },
  // 获取排名
  * getRankInfo(action, ctx, put) {
    // 如果开了降级，就不调接口
    const { degradeScanUpgrade } = yield '';
    if (degradeScanUpgrade) { return; }
    const data = yield pdaQueryRankInfo({ rankTypeCode: 5 });
    if (data.code === '0') {
      yield put((draft) => {
        draft.boxDisabled = 1;
      });
      classFocus('box');
    } else {
      Modal.error({ content: data.msg || t('请求数据出错') });
    }
  },
  * scanBox(action, ctx, put) {
    markStatus('boxDisabled');
    const { isAutoStockPick } = yield '';
    const res = yield scanPickContainer({
      containerCode: action,
      isAutoStockPick,
    });
    if (res.code !== '0') {
      yield put((draft) => {
        draft.containerCode = '';
      });
      Modal.error({
        content: res.msg,
        onOk: () => classFocus('box'),
      });
    } else {
      // 老逻辑：普通领取任务进来且扫描的也是普通周转箱
      if (!res.info?.isAutoStockPick && !isAutoStockPick) {
        yield put((draft) => {
          draft.info = res.info;
          draft.status = 'normalPage';
          // draft.orderType = res.info.orderType;
          draft.containerDisabled = true;
        });
        if (action !== res.info.containerCode) {
          Modal.confirm({
          // title: t('提示'),
            content: t('有未关箱的任务'),
            okText: t('是'),
            cancelText: t('否'),
            onOk: () => classFocus('location'),
          });
        } else {
          classFocus('location');
        }
        return;
      }
      const { isUserPicking } = res.info || {};
      // 存在未完成的任务
      if (isUserPicking) {
        // 需要将任务类型切换到未完成的任务类型
        yield this.changeData({
          data: {
            isAutoStockPick: !!res.info?.isAutoStockPick,
          },
        });
        const status = yield new Promise((r) => Modal.confirm({
          content: getTipsInfo(isAutoStockPick, !!res.info?.isAutoStockPick),
          onOk: () => r(1),
          onCancel: () => r(2),
        }));
        if (status === 1) {
          if (res.info.isAutoStockPick) {
            yield this.changeData({
              data: {
                info: res.info,
                containerCode: res.info?.containerCode || '',
                locationInput: res.info?.location || '',
                status: 'normalPage',
                containerDisabled: true,
                locationDisabled: 0,
                barCodeDisabled: 1,
              },
            });
            classFocus('barCode');
          } else {
            yield put((draft) => {
              draft.info = res.info;
              draft.status = 'normalPage';
              // draft.orderType = res.info.orderType;
              draft.containerDisabled = true;
            });
            classFocus('location');
          }
        } else {
          yield this.changeData({
            data: {
              containerCode: '',
            },
          });
          classFocus('box');
        }
      } else {
        if (isAutoStockPick) {
          yield this.changeData({
            data: {
              status: 'normalPage',
              containerDisabled: true,
            },
          });
          classFocus('location');
        } else {
          yield put((draft) => {
            draft.info = res.info;
            draft.status = 'normalPage';
            // draft.orderType = res.info.orderType;
            draft.containerDisabled = true;
          });
          classFocus('location');
        }
      }
    }
  },
  * scanLocation(action, ctx) {
    yield ctx.changeData({
      data: {
        locationDisabled: 0,
      },
    });
    if (action.info.location.toUpperCase() === action.params.location.toUpperCase()) {
      yield ctx.changeData({
        data: {
          barCodeDisabled: 1,
        },
      });
      classFocus('barCode');
    } else {
      if (action.params.location.toUpperCase() === action.params.containerCode.toUpperCase()) {
        const status = yield new Promise((r) => Modal.confirm({
          content: t('是否确认关箱'),
          onOk: () => r(1),
          onCancel: () => r(2),
        }));
        if (status === 1) {
          yield ctx.closeBox({
            params: {
              containerCode: action.params.containerCode,
            },
            names: {
              name: 'locationInput',
              disabled: 'locationDisabled',
              className: 'location',
            },
          });
        }
        if (status === 2) {
          yield ctx.changeData({
            data: {
              locationInput: '',
              locationDisabled: 1,
            },
          });
          classFocus('location');
        }
      } else {
        yield ctx.changeData({
          data: {
            locationInput: '',
            locationDisabled: 1,
          },
        });
        Modal.error({
          content: t('库位号扫描错误'),
          onOk: () => {
            classFocus('location');
          },
        });
      }
    }
  },
  * closeBox(action, ctx, put) {
    const res = yield closePickContainer(action.params);
    if (res.code !== '0') {
      yield put((draft) => {
        draft[action.names.name] = '';
        draft[action.names.disabled] = 1;
      });
      Modal.error({
        content: res.msg,
        onOk: classFocus(action.names.className),
      });
    } else {
      yield put((draft) => {
        draft.containerCode = '';
        draft.containerDisabled = false;
        draft.locationInput = '';
        draft.locationDisabled = 1;
        draft.goodsSnPrint = '';
        draft.barCodeDisabled = 1;
      });
      classFocus('box');
    }
  },
  * scanGoods(action, ctx, put) {
    yield put((draft) => {
      draft.barCodeDisabled = 0;
      draft.goodsSnPrint = '';
    });
    if (action.params.containerCode === action.params.goodsSnPrint) {
      const status = yield new Promise((r) => Modal.confirm({
        content: t('是否确认关箱'),
        onOk: () => r(1),
        onCancel: () => r(2),
      }));
      if (status === 1) {
        yield ctx.closeBox({
          params: {
            containerCode: action.params.containerCode,
          },
          names: {
            name: 'goodsSnPrint',
            disabled: 'barCodeDisabled',
            className: 'barCode',
          },
        });
      }
      if (status === 2) {
        yield ctx.changeData({
          data: {
            locationInput: '',
            locationDisabled: 1,
          },
        });
        classFocus('location');
      }
    } else {
      const res = yield scanGoods({
        goodsBarcode: action.params.goodsSnPrint,
        location: action.params.location,
        orderNo: action.params.orderNo,
        pickContainerCode: action.params.containerCode,
        taskDetailId: action.params.taskDetailId,
        taskCode: action.params.taskCode,
      });
      if (res.code !== '0') {
        yield put((draft) => {
          draft.barCodeDisabled = 1;
        });
        Modal.error({
          content: res.msg,
          onOk: () => {
            classFocus('barCode');
          },
        });
      } else {
        // isLocationBoxEmpty 为 true
        // 1：如果需要顶部提示，不需要弹窗，就仅仅顶部提示
        // 2：如果需要顶部提示，又需要弹窗，就把顶部提示的内容加到弹窗里面
        const { isLocationBoxEmpty } = res.info;
        if (isLocationBoxEmpty && res.info.backFlag !== 1) {
          message.info(t('拣货完成，请将库位周转箱下架'));
        } else if (res.info.backFlag === 1) {
          let contentMessage = t('任务已结束，点击确定返回任务领取页面');
          if (res.info.outSubWarehouseName) {
            contentMessage = `${t('任务已结束，请送往')}${res.info.outSubWarehouseName}${t('进行出库作业')}`;
          }
          let contentMessage1 = '';
          if (isLocationBoxEmpty) {
            contentMessage1 = t('库位已无库存，请将库位周转箱下架');
          }
          const status = yield new Promise((r) => Modal.success({
            content: (
              <div className={style.modalStyle}>
                <p>{contentMessage}</p>
                {contentMessage1 && (<p>{contentMessage1}</p>)}
              </div>
            ),
            okText: t('确定'),
            onOk: () => r(1),
          }));
          if (status === 1) {
            yield this.init();
            yield this.getInfo();
          }
        }
        if (res.info.backFlag === 2) {
          const nextLocation = (res.info.pdaStandardOperateDetailRsp || {}).location;
          // 判断是否需要重新扫描货位号
          if (action.params.location === nextLocation) {
            yield put((draft) => {
              draft.barCodeDisabled = 1;
            });
            classFocus('barCode'); // 光标继续置于扫描商品处
          } else {
            yield ctx.changeData({
              data: {
                locationInput: '',
                locationDisabled: 1,
                barCodeDisabled: 0,
              },
            });
            classFocus('location'); // 光标置于货位号
          }
          // 当前页面重新加载信息
          yield ctx.changeData({
            data: {
              info: res.info.pdaStandardOperateDetailRsp,
            },
          });
        }
      }
    }
  },
  * shortPick(action, ctx, put) {
    markStatus('dataLoading');
    const result = yield shortPick(action.params);
    if (result.code === '0') {
      // 继续下一件
      if (result.info.backFlag === 2) {
        yield put((draft) => {
          draft.locationInput = '';
          draft.locationDisabled = 1;
          draft.barCodeDisabled = 1;
          draft.goodsSnPrint = '';
          draft.info = result.info.pdaStandardOperateDetailRsp;
        });
        classFocus('location'); // 光标置于货位号
      } else {
        // 跳入领取页面
        const changeStatus = yield new Promise((r) => {
          Modal.success({
            content: t('拣货完成，点击确定返回任务领取页面'),
            onOk: () => r(true),
            autoFocusButton: null,
          });
        });
        if (changeStatus) {
          yield this.init();
          yield this.getInfo();
        }
      }
    } else {
      yield put((draft) => {
        draft.containerDisabled = true;
        draft.locationInput = '';
        draft.locationDisabled = 1;
        draft.barCodeDisabled = 0;
        draft.goodsSnPrint = '';
      });
      Modal.error({
        content: result.msg,
        onOk: () => {
          classFocus('location');
        },
      });
    }
  },
  * getDetail(action, ctx, put) {
    markStatus('dataLoading');
    const res = yield getDetailApi(action.params);
    if (res.code === '0') {
      const { hasPickList, waitingPickList } = res.info;
      yield put((draft) => {
        draft.showDetail = true;
        draft.detailList = [hasPickList || [], waitingPickList || []];
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },

  // PP下架扫库位
  * scanPPLocation(action) {
    markStatus('dataLoading');
    const { params } = action;
    const res = yield scanPPLocationAPI(params);
    if (res.code === '0') {
      // 加载页面信息
      yield this.changeData({
        data: {
          locationInput: res.info?.location || '',
          locationDisabled: 0,
          barCodeDisabled: 1,
          info: res.info || {},
        },
      });
      classFocus('barCode');
    } else {
      yield this.changeData({
        data: {
          locationInput: '',
          locationDisabled: 1,
        },
      });
      Modal.error({
        content: res.msg,
        onOk: () => {
          classFocus('location');
        },
      });
    }
  },
};
