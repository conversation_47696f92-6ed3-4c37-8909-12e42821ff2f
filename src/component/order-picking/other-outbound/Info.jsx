import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import style from './style.css';

function Info(props) {
  const {
    data,
    openPicturePickingModel,
  } = props;
  const location = data.location || '';
  return (
    <div>
      {!openPicturePickingModel && (
        <Form>
          <div className={style.firstItem} style={{ padding: '0 2px' }}>
            <span>{data.orderNo}</span>
            <div>
              {t('已拣/总件数')}:
              <span className={style.weight}>
                <span style={{ color: '#ff9636' }}>{data.hadOffNum}</span>
                /{data.hadOperateNum}
              </span>
            </div>
          </div>
          <div className={style.firstItem} style={{ padding: '0 2px' }}>
            <span>{t('出库作业子仓')}</span>
            <div>
              {data.outSubWarehouseName}
            </div>
          </div>
        </Form>
      )}
      <Form style={{
        marginTop: '10px',
      }}
      >
        <div className={style.mainInfo}>
          <div className={style.boxFloat}>
            <div className={style.floatLeft}>{t('货位号')}</div>
            <div className={style.floatRight}>
              <span style={{ fontSize: 16, fontWeight: 'normal' }}>
                <span>{location.substring(0, 6)}</span>
                <span style={{ fontSize: 20, fontWeight: 'bold' }}>{location.substring(6, 14)}</span>
                <span style={{ color: '#f00' }}>{location.substring(14)}</span>
                { data.sequence ? <span style={{ color: 'red', fontWeight: 'bold' }}>--{data.sequence}</span> : '' }
              </span>
            </div>
          </div>
          {openPicturePickingModel ? (
            <div className={style.mainItem}>
              <span>{t('SKC')}</span>
              <span style={{ color: 'red' }}>{data.size}</span>
              <div style={{ color: 'red' }}>
                {data.goodsSn}
              </div>
            </div>
          ) : (
            <>
              <div className={style.mainItem}>
                <span>{t('SKC')}</span>
                <div style={{ color: 'red' }}>
                  {data.goodsSn}
                </div>
              </div>
              <div className={classnames(style.mainItem, style.sizeAndColorItem)}>
                <div
                  style={{
                    fontSize: 25,
                    fontWeight: 'bold',
                    color: '#FF0000',
                    marginRight: 50,
                  }}
                >
                  {data.color}
                </div>
                <div style={{ fontSize: 25, fontWeight: 'bold', color: '#FF0000' }}>
                  {data.size}
                </div>
              </div>
            </>
          )}
        </div>
      </Form>
    </div>
  );
}

Info.propTypes = {
  data: PropTypes.shape(),
  openPicturePickingModel: PropTypes.bool,
};

export default Info;
