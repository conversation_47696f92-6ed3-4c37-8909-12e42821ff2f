import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import LoadMore from 'react-weui/build/packages/components/loadmore';
import {
  Cells, Cell, CellBody,
} from 'react-weui/build/packages/components/cell';
import {
  Footer,
  Header,
  pages,
} from 'common/index';
import { classFocus } from 'lib/util';
import BaseData from './base-data';
import store from './reducers';

const { View } = pages;

const InitPage = (props) => {
  const {
    dataLoading,
    baseData,
    headerTitle,
  } = props;
  return (
    <div>
      <Header title={headerTitle || t('其他出库')} />
      <View diff={100}>
        {dataLoading === 0 ? (
          <LoadMore loading={dataLoading === 0} />
        ) : (
          <BaseData data={baseData} hideSky />
        )}
        <Cells>
          <Cell
            onClick={() => {
              if (dataLoading === 0) {
                return;
              }
              store.changeData({
                data: {
                  isAutoStockPick: false, status: 'default',
                },
              }).then(() => {
                classFocus('box');
              });
            }}
          >
            <CellBody>
              {t('领取下架任务')}
            </CellBody>
          </Cell>
          <Cell
            onClick={() => {
              if (dataLoading === 0) {
                return;
              }
              store.changeData({
                data: {
                  isAutoStockPick: true,
                  status: 'default',
                },
              }).then(() => {
                classFocus('box');
              });
            }}
          >
            <CellBody>
              {t('领取自动化库区下架任务')}
            </CellBody>
          </Cell>
        </Cells>
      </View>
      <Footer beforeBack={(back) => {
        store.init();
        back();
      }}
      />
    </div>
  );
};

InitPage.propTypes = {
  dataLoading: PropTypes.number,
  headerTitle: PropTypes.string,
  baseData: PropTypes.shape(),
};

export default InitPage;
