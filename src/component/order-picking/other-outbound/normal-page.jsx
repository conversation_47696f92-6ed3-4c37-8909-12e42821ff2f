import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import { Form } from 'react-weui/build/packages/components/form';
import Footer from '../../common/footer';
import FooterBtn from '../../common/footer-btn';
import FocusInput from '../../common/focus-input';
import Info from './Info';
import Modal from '../../common/modal';
import RowInfo from '../../common/row-info';
import store from './reducers';
import Header from '../../common/header';
import style from './style.css';

function NormalPage(props) {
  const {
    containerDisabled,
    locationDisabled,
    barCodeDisabled,
    info,
    locationInput,
    headerTitle,
    // eslint-disable-next-line no-unused-vars
    containerCode,
    dataLoading,
    openPicturePickingModel,
    isAutoStockPick,
  } = props;
  return (
    <div>
      <Header title={headerTitle || t('其他出库')}>
        {dataLoading === 0 || !locationInput || locationInput !== info.location ? (
          <div style={{ color: 'gray' }}>
            <Icon name="duanjian" style={{ marginRight: 5 }} />
            {t('短拣')}
          </div>
        ) : (
          <div
            onClick={() => {
              Modal.confirm({
                content: t('确认是否短拣当前商品？'),
                okText: t('是'),
                cancelText: t('否'),
                onOk: () => {
                  store.shortPick({
                    params: {
                      pickContainerCode: info.containerCode,
                      orderNo: info.orderNo,
                      taskDetailId: info.taskDetailId,
                      taskCode: info.taskCode,
                    },
                    status: 'normalPage',
                  });
                },
              });
            }}
          >
            <Icon name="duanjian" style={{ marginRight: 5 }} />
            {t('短拣')}
          </div>
        )}
      </Header>
      <Form>
        <Info data={info} openPicturePickingModel={openPicturePickingModel} />
        <RowInfo
          extraStyle={{ justifyContent: 'flex-start' }}
          textExtraStyle={{
            width: '32%',
            color: '#FF0000',
            fontWeight: 'bold',
            fontSize: 20,
          }}
          label={t('待拣数量')}
          content={info.needOperateNum}
        />
      </Form>
      {/* 图片拣货模式 */}
      {/* 是否开启图片拣货模式 */}
      {openPicturePickingModel && (
        <img
          className={style.imgCss} src={info.imageUrl} onClick={() => {
            Modal.img({
              modalBlurInput: true,
              content: <img width="100%" src={info?.imageUrl} />,
            });
          }} alt=""
        />
      )}
      <Form
        style={{ marginTop: '10px' }}
      >
        <div style={{ position: 'relative' }}>
          <FocusInput
            importance
            data-bind="containerCode"
            disabled={containerDisabled}
            className="box"
            onPressEnter={(e) => {
              if (e.target.value) {
                store.scanBox(e.target.value);
              }
            }}
          >
            <label>{t('拣货周转箱')}</label>
          </FocusInput>
        </div>
        {isAutoStockPick ? (
          <FocusInput
            placeholder={t('请扫描')}
            data-bind="locationInput"
            onPressEnter={(e) => {
              if (e.target.value) {
                store.scanPPLocation({
                  params: {
                    location: locationInput,
                    pickContainerCode: containerCode,
                    isAutoStockPick: true,
                  },
                });
              }
            }}
            disabled={locationDisabled === 0 || dataLoading === 0}
            className="location"
          >
            <label>{t('货位号')}</label>
          </FocusInput>
        ) : (
          <FocusInput
            placeholder={t('请扫描')}
            data-bind="locationInput"
            onPressEnter={(e) => {
              if (e.target.value) {
                store.scanLocation({
                  params: {
                    containerCode: info.containerCode,
                    location: locationInput,
                  },
                  waitOffNum: info.waitOffNum,
                  shiftOrderCode: info.shiftOrderCode,
                  info,
                });
              }
            }}
            disabled={locationDisabled === 0}
            className="location"
          >
            <label>{t('货位号')}</label>
          </FocusInput>
        )}

        <FocusInput
          placeholder={t('请扫描')}
          data-bind="goodsSnPrint"
          onPressEnter={(e) => {
            if (e.target.value) {
              store.scanGoods({
                params: {
                  containerCode: info.containerCode,
                  goodsSn: info.goodsSn,
                  goodsSnPrint: e.target.value,
                  location: locationInput,
                  orderNo: info.orderNo,
                  taskDetailId: info.taskDetailId,
                  taskCode: info.taskCode,
                },
                info,
              });
            }
          }}
          disabled={barCodeDisabled === 0}
          className="barCode"
        >
          <label>{t('商品条码')}</label>
        </FocusInput>
      </Form>
      <Footer
        beforeBack={() => {
          store.init();
          store.getInfo();
        }}
      >
        <FooterBtn
          onClick={() => {
            store.getDetail({
              params: {
                // pickContainerCode: containerCode,
                taskCode: info.taskCode,
              },
            });
          }}
        >
          {t('明细')}
        </FooterBtn>
      </Footer>
    </div>
  );
}

NormalPage.propTypes = {
  dataLoading: PropTypes.number,
  headerTitle: PropTypes.string,
  containerCode: PropTypes.string,
  info: PropTypes.shape(),
  openPicturePickingModel: PropTypes.bool,
  containerDisabled: PropTypes.bool,
  locationDisabled: PropTypes.bool,
  barCodeDisabled: PropTypes.bool,
  locationInput: PropTypes.string,
  isAutoStockPick: PropTypes.bool,
};

export default NormalPage;
