import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import DefaultPage from './default';
import NormalPage from './normal-page';
import DetailPage from './detail-page';
import InitPage from './init-page';
import store from './reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
    store.getInfo();
  }

  render() {
    const {
      status,
      showDetail,
      detailList,
      dispatch,
    } = this.props;
    let children;
    switch (status) {
      case 'initPage':
        children = (<InitPage {...this.props} />);
        break;
      case 'default':
        children = (<DefaultPage {...this.props} />);
        break;
      case 'normalPage':
        children = (<NormalPage {...this.props} />);
        break;
      default:
        break;
    }
    if (showDetail) {
      children = (
        <DetailPage
          data={detailList}
          dispatch={dispatch}
        />
      );
    }
    return (
      <div style={{ marginBottom: '56px' }}>
        {children}
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  status: PropTypes.string,
  showDetail: PropTypes.bool,
  detailList: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Container);
