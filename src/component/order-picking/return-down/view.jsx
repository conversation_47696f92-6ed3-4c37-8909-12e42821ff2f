import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import DefaultPage from './default';
import NormalPage from './normal-page';
import DetailPage from './detail-page';
import store from './reducers';
import { Header } from '../../common';
import navStore from '../../nav/reducers';
import { classFocus } from '../../../lib/util';

class Container extends Component {
  componentDidMount() {
    store.getInfo();
  }

  render() {
    const {
      status,
      headerTitle,
      info,
      showDetail,
      detailList,
      dispatch,
    } = this.props;
    let children;
    switch (status) {
      case 'default':
        children = (<DefaultPage {...this.props} />);
        break;
      case 'normalPage':
        children = (<NormalPage {...this.props} />);
        break;
      default:
        break;
    }
    if (showDetail) {
      children = (
        <DetailPage
          data={detailList}
          dispatch={dispatch}
        />
      );
    }
    return (
      <div>
        <Header title={headerTitle || t('采购退货下架')} />
        {children}
        {/*<DragCircle*/}
        {/*  onClick={() => {*/}
        {/*    navStore.changeData({ data: { showUploadError: true } });*/}
        {/*    navStore.changeLimit({ data: { location: info && info.location ? info.location : '' } });*/}
        {/*    classFocus('location');*/}
        {/*  }}*/}
        {/*/>*/}
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  headerTitle: PropTypes.string,
  info: PropTypes.shape(),
  showDetail: PropTypes.bool,
  status: PropTypes.string,
  detailList: PropTypes.arrayOf(PropTypes.array),
};

export default i18n(Container);
