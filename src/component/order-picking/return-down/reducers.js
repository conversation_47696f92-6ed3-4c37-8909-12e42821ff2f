import assign from 'object-assign';
import { select } from 'redux-saga/effects';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { t } from '@shein-bbl/react';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import Modal from '../../common/modal';
import {
  pdaQueryRankInfo, queryPurchaseReturnInfo, scanBox, scanLocation,
  closeBox, scanSku, pdaShortPick, submitGoodsCount, getDetailApi,
} from './server';

const defaultState = {
  status: 'default',
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  baseData: [
    {
      label: t('待领取任务数'),
      num: 0,
      type: 'info',
    },
    {
      label: t('已下架件数'),
      num: 0,
      type: 'warn',
    },
    {
      label: t('排名'),
      num: 0,
      type: 'sky',
    },
  ],
  containerCode: '', // 外层周转箱
  boxDisabled: 1,
  info: {},
  num: '',
  orderType: '',
  locationInput: '',
  goodsSnPrint: '',
  containerDisabled: false,
  locationDisabled: 1,
  barCodeDisabled: 1,
  numDisabled: 1,
  headerTitle: '',
  showDetail: false,
  detailList: [[], []],
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  * getInfo(action, ctx, put) {
    markStatus('dataLoading');
    const res = yield queryPurchaseReturnInfo();
    const data = yield pdaQueryRankInfo({ rankTypeCode: 6 });
    if (res.code === '0' && data.code === '0') {
      yield put((draft) => {
        draft.baseData[0].num = res.info.waitTaskNum;
        draft.baseData[1].num = res.info.downNum;
        draft.baseData[2].num = data.info.rank;
        draft.boxDisabled = false;
      });
      classFocus('box');
    } else if (res.code === '0' && data.code !== '0') {
      Modal.error({ content: data.msg, className: 'box' });
    } else if (res.code !== '0' && data.code === '0') {
      Modal.error({ content: res.msg, className: 'box' });
    } else {
      Modal.error({ content: t('请求数据出错'), className: 'box' });
    }
  },
  * scanBox(action, ctx, put) {
    markStatus('boxDisabled');
    const res = yield scanBox({
      containerCode: action,
      warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId,
      warehouseName: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseName,
    });
    // const res = {"code":"0","msg":"ok","info":{"containerCode":"jh0719140","shiftOrderCode":"mv19080600005","replenishmentCode":"bh19080600017","goodsTotalNum":174,"location":"dh0801-03","goodsSn":"vest180410713","size":"s","waitOffNum":71,"hadOffNum":17,"orderType":"采购退货出库","color":null,"returnSubWarehouse":'退供子仓'}}
    if (res.code !== '0') {
      yield put((draft) => {
        draft.containerCode = '';
      });
      Modal.error({
        content: res.msg,
        onOk: () => classFocus('box'),
      });
    } else {
      yield put((draft) => {
        draft.info = res.info;
        draft.status = 'normalPage';
        // draft.orderType = res.info.orderType;
        draft.containerDisabled = true;
        draft.containerCode = res.info.containerCode;
      });
      if (action !== res.info.containerCode) {
        Modal.success({
          content: t('您存在未完成的任务'),
          onOk: () => classFocus('location'),
        });
      } else {
        classFocus('location');
      }
    }
  },
  * scanLocation(action, ctx, put) {
    yield put((draft) => {
      draft.locationDisabled = 0;
    });
    const res = yield scanLocation(action.params);
    // const res = {"code":"0","msg":"ok","info":{"type":1}};
    if (res.code !== '0') {
      yield put((draft) => {
        draft.locationDisabled = 1;
        draft.locationInput = '';
      });
      Modal.error({
        content: res.msg,
        onOk: () => classFocus('location'),
      });
    } else {
      if (res.info.type === 1) {
        yield put((draft) => {
          draft.barCodeDisabled = 1;
        });
        classFocus('barCode');
      } else if (res.info.type === 2) { // 关箱
        yield put((draft) => {
          draft.locationDisabled = 1;
        });
        const status = yield new Promise((r) => Modal.confirm({
          content: t('是否确认关箱'),
          onOk: () => r(1),
          onCancel: () => r(2),
        }));
        if (status === 1) {
          yield ctx.closeBox({
            params: {
              containerCode: action.params.containerCode,
              shiftOrderCode: action.params.shiftOrderCode,
            },
            names: {
              name: 'locationInput',
              disabled: 'locationDisabled',
              className: 'location',
            },
          });
        }
        if (status === 2) {
          yield ctx.changeData({
            data: {
              locationInput: '',
              locationDisabled: 1,
            },
          });
          setTimeout(() => {
            classFocus('location');
          }, 300);
        }
      }
    }
  },
  * closeBox(action, ctx) {
    const res = yield closeBox(action.params);
    if (res.code !== '0') {
      Modal.error({
        content: res.msg,
        onOk: classFocus(action.names.className),
      });
    } else {
      const status = yield new Promise((r) => Modal.success({
        content: t('关箱成功'),
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
        yield ctx.getInfo();
      }
    }
  },
  * scanGoods(action, ctx, put) {
    yield put((draft) => {
      draft.barCodeDisabled = 0;
    });
    const res = yield scanSku(action.params);
    // const res = {"code":"0","msg":"ok","info":{"type":1}};
    if (res.code !== '0') {
      yield put((draft) => {
        draft.barCodeDisabled = 1;
        draft.goodsSnPrint = '';
      });
      Modal.error({
        content: res.msg,
        onOk: classFocus('barCode'),
      });
    } else {
      if (res.info.type === 1) {
        yield put((draft) => {
          draft.numDisabled = 1;
        });
        classFocus('num');
      } else if (res.info.type === 2) {
        yield ctx.closeBox({
          params: {
            containerCode: action.params.containerCode,
            shiftOrderCode: action.params.shiftOrderCode,
          },
          names: {
            name: 'goodsSnPrint',
            disabled: 'barCodeDisabled',
            className: 'barCode',
          },
        });
      }
    }
  },
  * shortPick(action, ctx, put) {
    const { status, info } = yield select((state) => state['order-picking/return-down']);
    const res = yield pdaShortPick(action.params);
    if (res.code !== '0') {
      yield put((draft) => {
        draft.locationInput = '';
        draft.locationDisabled = 1;
        draft.containerCode = '';
        draft.containerDisabled = false;
        draft.barCodeDisabled = 1;
        draft.goodsSnPrint = '';
      });
      Modal.error({
        content: res.msg,
      });
    } else {
      if (res.info.type === 2) {
        yield ctx.init();
      } else {
        yield put((draft) => {
          draft.status = status;
          draft.locationInput = '';
          draft.locationDisabled = 1;
          draft.goodsSnPrint = '';
          draft.num = '';
          draft.info = assign({}, info, res.info.taskGoodsInfo);
        });
        classFocus(status === 'normalPage' ? 'box' : 'location');
      }
    }
  },
  * submitCount(action, ctx, put) {
    markStatus('numDisabled');
    const res = yield submitGoodsCount(action.params);
    // const res = {"code":"0","msg":"ok","info":{"type":1,"taskGoodsInfo":{"location":"dh0801-03","goodssn":"vest180410713","waitoffnum":69,"hadoffnum":19,"size":"s"}}}
    if (res.code !== '0') {
      yield put((draft) => {
        draft.num = '';
      });
      Modal.error({
        content: res.msg,
        onOk: () => {
          classFocus('num');
        },
      });
    } else {
      if (res.info.type === 1) {
        if (res.info.taskGoodsInfo) {
          const {
            location,
          } = res.info.taskGoodsInfo;
          yield ctx.changeData({
            data: {
              info: assign({}, action.info, res.info.taskGoodsInfo),
            },
          });
          if (action.info.location !== location) {
            yield ctx.changeData({
              data: {
                num: '',
                goodsSnPrint: '',
                locationInput: '',
                locationDisabled: 1,
                barCodeDisabled: 1,
              },
            });
            Modal.success({
              content: t('成功下架') + action.params.num + t('件'),
              onOk: () => {
                classFocus('location');
              },
            });
          } else {
            yield ctx.changeData({
              data: {
                num: '',
              },
            });
            Modal.success({
              content: t('成功下架') + action.params.num + t('件'),
              onOk: () => {
                classFocus('num');
              },
            });
          }
        }
        return;
      }

      if (res.info.type === 2) {
        const type2 = yield new Promise((r) => Modal.confirm({
          content: t('是否确认下架'),
          okText: t('确认'),
          onOk: () => r(1),
          onCancel: () => r(2),
        }));
        if (type2 === 1) {
          yield ctx.submitCount({
            params: assign({}, action.params, { force: true }),
            info: action.info,
          });
        }
        if (type2 === 2) {
          setTimeout(() => classFocus('num'), 0);
        }

        return;
      }

      if (res.info.type === 3) {
        const status = yield new Promise((r) => Modal.success({
          content: t('该任务下架完成'),
          okText: t('确认'),
          onOk: () => r(1),
        }));
        if (status === 1) {
          yield ctx.init();
          yield ctx.getInfo();
        }
        return;
      }

      if (res.info.type === 4) {
        const type4 = yield new Promise((r) => Modal.confirm({
          content: t('是否确认短拣'),
          okText: t('确认'),
          onOk: () => r(1),
          onCancel: () => r(2),
        }));
        if (type4 === 1) {
          yield ctx.shortPick({
            params: assign({}, action.params),
          });
        }
        if (type4 === 2) {
          setTimeout(() => classFocus('num'), 0);
        }
      }
    }
  },
  * getDetail(action, ctx, put) {
    markStatus('dataLoading');
    const res = yield getDetailApi(action.params);
    if (res.code === '0') {
      yield put((draft) => {
        draft.showDetail = true;
        draft.detailList = [res.info.hasPickedRecords || [], res.info.notPickedRecords || []];
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
};
