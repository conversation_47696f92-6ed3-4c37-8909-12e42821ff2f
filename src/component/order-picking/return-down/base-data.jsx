import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages/components/form';
import RowInfo from '../../common/row-info';

const BaseData = (props) => {
  const {
    data,
  } = props;
  return (
    <div>
      <Form>
        {
          data.map(item => (
            <RowInfo
              key={item.label}
              extraStyle={{
                borderBottom: 'none',
              }}
              label={item.label}
              content={item.num}
              type={item.type}
            />
          ))
        }
      </Form>
    </div>
  );
};

BaseData.propTypes = {
};

export default BaseData;
