import { sendPostRequest } from '../../../lib/public-request';
/**
 * 查询当前用户排名
 * @param param
 * @returns {*}
 */
export const pdaQueryRankInfo = (param) => sendPostRequest({
  baseUrl: process.env.WKB,
  url: '/rank/query',
  param,
});
export const queryPurchaseReturnInfo = (param) => sendPostRequest({
  url: '/replenish_shelves/query_purchase_return_task_info',
  param,
});
/**
 * 扫描库位
 * @param param
 * @returns {*}
 */
export const scanLocation = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_location',
  param,
});
/**
 * 扫描托盘号
 * @param param
 * @returns {*}
 */
export const scanPallet = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_pallet',
  param,
});
/**
 * 短拣
 * @param param
 * @returns {*}
 */
export const shortPick = (param) => sendPostRequest({
  url: '/replenish_shelves/short_pick',
  param,
}, process.env.WWS_URI);

export const scanBox = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_purchase_return_pick_container',
  param,
});
export const scanSku = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_purchase_return_barcode',
  param,
});

export const submitGoodsCount = (param) => sendPostRequest({
  url: '/replenish_shelves/submit_purchase_return_goods_count',
  param,
});
export const closeBox = (param) => sendPostRequest({
  url: '/replenish_shelves/close_purchase_return_pick_container',
  param,
});

export const pdaShortPick = (param) => sendPostRequest({
  url: '/replenish_shelves/purchase_return_short_pick',
  param,
});

export const getDetailApi = (param) => sendPostRequest({
  url: '/replenish_shelves/query_record_detail',
  param,
});
