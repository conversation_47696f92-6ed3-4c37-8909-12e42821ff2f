// detail-page 明细

import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { NavDetail, pages, Footer } from '../../../common/index';
import store, { underWayMap } from '../reducers';

const { View } = pages;

const rows1 = [
  [
    {
      title: t('SKC'),
      render: 'goodsSn',
    },
    {
      title: t('数量'),
      render: 'alreadyNum',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('尺码'),
      render: 'size',
    },
    {
      title: t('库位'),
      render: 'location',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('周转箱'),
      render: 'containerCode',
    },
  ],
];
const rows2 = [
  [
    {
      title: t('SKC'),
      render: 'goodsSn',
    },
    {
      title: t('数量'),
      render: 'waitNum',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('尺码'),
      render: 'size',
    },
    {
      title: t('库位'),
      render: 'location',
      itemRenderStyle: { color: 'red' },
    },
  ],
];

const rows3 = [
  [
    {
      title: t('SKC'),
      render: 'goodsSn',
    },
    {
      title: t('数量'),
      render: 'waitNum',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('尺码'),
      render: 'size',
    },
    {
      title: t('库位'),
      render: 'location',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('周转箱'),
      render: 'containerCode',
    },
  ],
];

function DetailPage(props) {
  const {
    data,
    info,
    dispatch,
  } = props;

  const rowsList = [underWayMap.get(t('整箱')), underWayMap.get(t('炬星'))].includes(info.underWay) ? [rows1, rows3] : [rows1, rows2];
  const hasPickNum = (data[0] || []).reduce((pre, current) => pre + current.alreadyNum, 0);
  const noPickkNum = (data[1] || []).reduce((pre, current) => pre + current.waitNum, 0);
  const totalNumList = [hasPickNum, noPickkNum];
  const navList = [t('已拣选'), t('未拣选')];
  return (
    <View diff={100}>
      <NavDetail
        data={data}
        rowsList={rowsList}
        navList={navList}
        imgUrlFieldName="imageUrl"
        totalNumList={totalNumList}
      />
      <Footer
        dispatch={dispatch}
        beforeBack={() => {
          store.changeData({
            data: {
              showDetail: false,
            },
          });
        }}
      />
    </View>
  );
}

DetailPage.propTypes = {
  data: PropTypes.arrayOf(PropTypes.array),
  dispatch: PropTypes.func,
  info: PropTypes.shape(),
};

export default DetailPage;
