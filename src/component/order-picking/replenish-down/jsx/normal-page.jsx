// normal-page 逐件下架
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Dialog, Form, Button } from 'react-weui/build/packages';
import UserIntro from 'common/user-intro';
import { LOCAL_REPLENISH_DOWN_PICKER } from 'lib/storage';
import { classFocus, isaNumberInRange } from '../../../../lib/util';
import {
  FooterBtn,
  Footer,
  FocusInput,
  modal,
  message,
  RowInfo,
  Header,
  Pickers,
} from '../../../common/index';
import IntroContext from '../../../user-intro-page/IntroContext';
import Info from './Info';
import store from '../reducers';
import navStore from '../../../nav/reducers';
import style from '../style.css';

const getMsg = (num, maxNum = 1) => {
  // 允许输入0，输入0表示全部短拣
  if (num === 0 || num === '0') {
    return '';
  }
  if (!num) {
    return t('拣货数不能为空');
  }
  const reg = /^[1-9][0-9]{0,}$/;
  if (!reg.test(num)) {
    return t('拣货数必须为正整数');
  }
  if (num > maxNum) {
    return t('拣货数不可超过待拣数量，短拣失误商品请放原位');
  }
  if (!isaNumberInRange(num, 0, maxNum)) {
    return t('拣货数范围为1-') + maxNum;
  }
  return '';
};

function NormalPage(props) {
  const {
    dispatch,
    containerDisabled,
    locationDisabled,
    barCodeDisabled,
    containerCode,
    info,
    orderType,
    orderTypeCode,
    locationInput,
    headerTitle,
    steps,
    shortPickType,
    countNumModal,
    countNumVal,
    waitOffNumInput,
    selectShow,
    pickerDataList,
    currentPicker,
    currentLabel,
  } = props;

  // 日常补货，或紧急补货逐件扫描开关=关闭时展示待下数
  const waitOffNumVisible = info && info.waitOffNum && (orderTypeCode === 2 || // 2.日常补货
    (orderTypeCode === 1 && shortPickType === 2)); // 1.紧急补货 && 逐件扫描开关=关闭

  useEffect(() => {
    navStore.changeData({ data: { pageStore: store } });
  });

  const handleBarCodePressEnter = async (value) => {
    const params = {
      containerCode: info.containerCode,
      goodsSn: info.goodsSn,
      skuCode: info.skuCode,
      goodsSnPrint: value,
      location: locationInput,
      replenishmentCode: info.replenishmentCode,
      size: info.size,
      shiftOrderCode: info.shiftOrderCode,
    };

    if (waitOffNumVisible && waitOffNumInput) {
      params.offNum = Number(waitOffNumInput);

      if (Number(info.autoFlag) === 1) {
        const status = await new Promise((r) => modal.confirm({
          content: t('请确定商品{}件已全部拣出?', params.offNum),
          onOk: () => { r('ok'); },
          onCancel: () => { r('cancel'); },
        }));
        if (status === 'cancel') {
          return;
        }
      }
    }

    if (waitOffNumVisible && (info.waitOffNum > waitOffNumInput)) {
      modal.confirm({
        content: t('确定是否关箱?'),
        onOk: () => {
          store.scanGoods({
            params,
            info,
            dispatch,
          });
        },
        onCancel: () => {
          store.changeData({
            data: {
              goodsSnPrint: '',
            },
          });
          classFocus('barCode');
        },
      });
    } else {
      store.scanGoods({
        params,
        info,
        dispatch,
      });
    }
  };

  return (
    <IntroContext.Consumer>
      {(context) =>
      // eslint-disable-next-line implicit-arrow-linebreak
        (
          <div>
            <Header title={headerTitle || t('补货下架')}>
              <div
                style={{ padding: '0 10px', color: locationInput ? '#fff' : '#999' }}
                onClick={() => {
                // 库位号为空，短拣置灰不能点击
                  if (!locationInput) {
                    return;
                  }
                  if (String(shortPickType) === '1') {
                  // 弹窗：紧急任务且配置为逐件扫描
                    modal.confirm({
                      content: (
                        <table width="100%">
                          <tbody>
                            <tr>
                              <td colSpan={2}>
                                {t('短拣明细如下,是否继续?')}
                              </td>
                            </tr>
                            <tr>
                              <td width="30%" align="right">
                                {t('库位号')} :
                              </td>
                              <td align="left">
                                {info.location}
                              </td>
                            </tr>
                            <tr>
                              <td width="30%" align="right">
                                {t('SKC')} :
                              </td>
                              <td align="left">
                                {info.goodsSn}
                              </td>
                            </tr>
                            <tr>
                              <td width="30%" align="right">
                                {t('尺码')} :
                              </td>
                              <td align="left">
                                {info.size}
                              </td>
                            </tr>
                            <tr>
                              <td width="30%" align="right">
                                {t('件数')} :
                              </td>
                              <td align="left">
                                {info.waitOffNum}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      ),
                      okText: t('继续'),
                      cancelText: t('取消'),
                      onOk: () => {
                        store.shortPick({
                          params: {
                            containerCode: info.containerCode,
                            location: info.location,
                            replenishmentCode: info.replenishmentCode,
                            shiftOrderCode: info.shiftOrderCode,
                          },
                          status: 'normalPage',
                          info,
                          dispatch,
                        });
                      },
                    });
                  } else {
                  // 弹窗：点数下架
                    store.changeData({ data: { countNumModal: true, countNumVal: '' } });
                    setTimeout(() => classFocus('countNumVal'), 20);
                  }
                }}
              >
                {t('短拣')}
              </div>
              {
              info.autoFlag && (
                <div
                  style={{ padding: '0 10px', color: '#fff' }}
                  onClick={() => {
                    store.autoEmptyBox({
                      params: {
                        containerCode: info.containerCode, // 周转箱号
                        location: info.location, // 货位号
                        replenishmentCode: info.replenishmentCode, // 补货单号(任务号)
                        shiftOrderCode: info.shiftOrderCode, // 移位单号
                      },
                    });
                  }}
                >
                  {t('此为空箱')}
                </div>
              )
            }
              <div />
            </Header>
            <UserIntro
              showIntro={context.showIntroVal}
              steps={steps}
              finishHandle={() => {
                store.changeData({ data: { status: 'default' } });
                classFocus('box');
              }}
            />
            <Form>
              <Info data={info} orderType={orderType} orderTypeCode={orderTypeCode} />
              <RowInfo
                extraStyle={{
                  borderBottom: 'none',
                }}
                label={currentLabel}
                content={(
                  <div>
                    <span className={style.num}>{currentPicker ? `${info.itemHadOffNum}/${info.itemWaitOffNum}` : info.waitOffNum}</span>
                    <Button
                      type="primary"
                      size="small"
                      onClick={() => {
                        store.changeData({ data: { selectShow: true } });
                      }}
                    >{t('模式')}
                    </Button>
                  </div>
              )}
              />
              <Pickers
                only
                value={currentPicker}
                defaultValue={currentPicker}
                label={t('选择子仓')}
                placeholder={t('请选择')}
                onClick={() => store.changeData({ data: { selectShow: true } })}
                onChange={(select) => {
                  store.changeData({
                    data: {
                      selectShow: false,
                      currentPicker: select.value,
                      currentLabel: select.label,
                    },
                  });
                  localStorage.setItem(LOCAL_REPLENISH_DOWN_PICKER, JSON.stringify(select));
                }}
                show={selectShow}
                pickerData={pickerDataList}
                onCancel={() => store.changeData({ data: { selectShow: false } })}
              />
            </Form>
            <Form
              style={{ marginTop: '10px' }}
            >
              <FocusInput
                importance
                data-bind="containerCode"
                placeholder={t('请扫描拣货周转箱')}
                disabled={containerDisabled}
                className="box"
                onPressEnter={(e) => {
                  if (e.target.value) {
                    store.scanBox({
                      params: {
                        containerCode: e.target.value,
                        shiftOrderCode: info.shiftOrderCode,
                        replenishmentCode: info.replenishmentCode,
                      },
                    });
                  }
                }}
              >
                <label>{t('拣货周转箱')}</label>
              </FocusInput>
              <FocusInput
                data-step="2"
                data-intro={t('第二步，扫描库位号')}
                placeholder={t('请扫描库位号')}
                data-bind="locationInput"
                onPressEnter={(e) => {
                  if (e.target.value) {
                    store.scanLocation({
                      params: {
                        containerCode: info.containerCode,
                        goodsSn: info.goodsSn,
                        skuCode: info.skuCode,
                        location: locationInput,
                        replenishmentCode: info.replenishmentCode,
                        size: info.size,
                      },
                      waitOffNum: info.waitOffNum,
                      shiftOrderCode: info.shiftOrderCode,
                    });
                  }
                }}
                disabled={locationDisabled === 0 || context.showIntroVal}
                className="location"
              >
                <label>{t('库位号')}</label>
              </FocusInput>
              {
              waitOffNumVisible && (
                <FocusInput
                  className="waitOffNum"
                  data-bind="waitOffNumInput"
                  onPressEnter={() => {
                    // 业务: 提交后光标跳转至商品条码文本框
                    classFocus('barCode');
                  }}
                  onChange={(e) => {
                    if (e.target.value) {
                      // 是否为正整数
                      const isPositiveInteger = (s) => /^[0-9]+$/.test(s);

                      let number = Number(e.target.value);

                      // 业务: 当输入的数量大于待下数量/为非正整数时，自动重置为当前待下数量
                      if (!isPositiveInteger(number) || number > Number(info.waitOffNum)) {
                        number = Number(info.waitOffNum);
                      }

                      store.changeData({ data: { waitOffNumInput: number } });
                    }
                  }}
                >
                  <label>{t('数量')}</label>
                </FocusInput>
              )
            }
              <FocusInput
                data-step="3"
                data-intro={t('第三步，扫描商品条码（任务全部完成系统会弹出“任务已结束”，点击确认）')}
                placeholder={t('请扫描商品条码')}
                data-bind="goodsSnPrint"
                disabled={barCodeDisabled === 0 || context.showIntroVal}
                className="barCode"
                onPressEnter={(e) => {
                  if (e.target.value) {
                    if (!containerCode) {
                      message.error(t('请先扫描拣货周转箱'));
                      classFocus('box');
                      return;
                    }
                    handleBarCodePressEnter(e.target.value);
                  }
                }}
              >
                <label>{t('商品条码')}</label>
              </FocusInput>
            </Form>
            <Footer
              beforeBack={() => {
                store.init();
              }}
            >
              <FooterBtn
                type="primary"
                onClick={() => {
                  store.getDetail({
                    params: {
                      shiftOrderCode: info.shiftOrderCode,
                      replenishmentCode: info.replenishmentCode,
                    },
                  });
                }}
              >
                {t('明细')}
              </FooterBtn>
            </Footer>
            <Dialog
              title={`${t('短拣')}`}
              show={countNumModal}
              buttons={[{
                label: t('取消'),
                type: 'default',
                onClick: () => store.changeData({ data: { countNumModal: false } }),
              }, {
                label: t('确认'),
                type: !countNumVal ? 'default' : 'primary',
                disabled: true,
                onClick: () => {
                // 模拟实现按钮禁用
                  if (!countNumVal) {
                    return;
                  }
                  const msg = getMsg(countNumVal, info.waitOffNum);
                  if (msg) {
                    message.error(msg);
                    store.changeData({ data: { countNumVal: '' } });
                    classFocus('countNumVal');
                    return;
                  }
                  store.changeData({ data: { countNumModal: false } });
                  store.shortPick({
                    params: {
                      containerCode: info.containerCode,
                      location: info.location,
                      replenishmentCode: info.replenishmentCode,
                      shiftOrderCode: info.shiftOrderCode,
                      countNum: countNumVal,
                    },
                    status: 'normalPage',
                    info,
                    locationInput, // 用于判断聚焦
                    dispatch,
                  });
                },
              }]}
            >
              <div style={{
                margin: '0 -10px', color: '#333', textAlign: 'left', fontSize: 14,
              }}
              >
                <div style={{ fontSize: 12 }}>{t('输入实际拣货数量后，系统自动计算短拣数量')}</div>
                <div style={{ marginTop: 5 }}>
                  {t('库位号')} : {info.location}
                </div>
                <div style={{ marginTop: 5 }}>
                  {t('SKC')} : {info.goodsSn}
                </div>
                <div style={{ marginTop: 5 }}>
                  {t('尺码')} : {info.size}
                </div>
                <div style={{ marginTop: 5 }}>
                  {t('待拣数量')} : {info.waitOffNum}
                </div>
                <div style={{ marginLeft: -14 }}>
                  <FocusInput
                    lineBreak={false}
                    placeholder={t('请输入拣货数数量')}
                    data-bind="countNumVal"
                    className="countNumVal"
                    labelShowStyleObj={{ width: 60 }}
                  >
                    <label>{t('拣货数')}:</label>
                  </FocusInput>
                </div>
              </div>
            </Dialog>
          </div>
        )}
    </IntroContext.Consumer>
  );
}

NormalPage.propTypes = {
  dispatch: PropTypes.func.isRequired,
  shortPickType: PropTypes.number,
  countNumModal: PropTypes.bool,
  countNumVal: PropTypes.number,
  headerTitle: PropTypes.string,
  containerCode: PropTypes.string,
  info: PropTypes.shape(),
  steps: PropTypes.arrayOf(PropTypes.shape()),
  waitOffNumInput: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  orderType: PropTypes.string,
  orderTypeCode: PropTypes.number,
  containerDisabled: PropTypes.bool,
  locationDisabled: PropTypes.number,
  barCodeDisabled: PropTypes.number,
  locationInput: PropTypes.string,
  selectShow: PropTypes.bool,
  pickerDataList: PropTypes.arrayOf(PropTypes.shape()),
  currentPicker: PropTypes.number,
  currentLabel: PropTypes.string,
};

export default NormalPage;
