// 自动化库区
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { Button } from 'react-weui/build/packages/components/button';
import LoadMore from 'react-weui/build/packages/components/loadmore';
import {
  Footer,
  Header,
  FocusInput,
} from 'common/index';
import IntroContext from '../../../user-intro-page/IntroContext';
import navStore from '../../../nav/reducers';
import BaseData from './base-data';
import store from '../reducers';

function AutoMovingPage(props) {
  const {
    dataLoading,
    baseData,
    headerTitle,
    autoPickContainerDisabled,
    autoPickContainer,
    autoLocationContainerDisabled,
    info,
  } = props;

  useEffect(() => {
    navStore.changeData({ data: { pageStore: store } });
  });

  return (
    <IntroContext.Consumer>
      {() => (
        <div>
          <Header title={headerTitle || t('任务领取')} />
          {
            dataLoading === 0 ?
              <LoadMore loading={dataLoading === 0} /> :
              <BaseData data={baseData} />
          }
          <Form>
            <FocusInput
              data-bind="autoPickContainer"
              disabled={autoPickContainerDisabled || dataLoading === 0}
              placeholder={t('请扫描周转箱')}
              className="autoPickContainer"
              onPressEnter={(e) => {
                if (e.target.value) {
                  const { value } = e.target;

                  store.scanAutoPickContainer({
                    params: {
                      containerCode: value,
                    },
                  });
                }
              }}
            >
              <label>{t('周转箱')}</label>
            </FocusInput>
          </Form>
          <Form>
            <FocusInput
              data-bind="autoLocationContainer"
              disabled={autoLocationContainerDisabled || dataLoading === 0}
              placeholder={t('请扫描周转箱')}
              className="autoLocationContainer"
              onPressEnter={(e) => {
                if (e.target.value) {
                  const { value } = e.target;

                  store.scanAutoLocationContainer({
                    params: {
                      containerCode: autoPickContainer,
                      locationContainerCode: value,
                    },
                  });
                }
              }}
            >
              <label>{t('库位周转箱')}</label>
            </FocusInput>
          </Form>
          {
            info.containerCode && (
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                padding: '6px 12px',
                fontSize: '15px',
                borderBottom: '1px solid #e8ebf0',
              }}
              >
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
                >
                  {t('作业中')}
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
                >
                  {info.containerCode}
                </div>
                <Button
                  disabled={dataLoading === 0}
                  size="small"
                  style={{ margin: '0' }}
                  onClick={() => {
                    store.closeBox({
                      params: {
                        containerCode: info.containerCode,
                        shiftOrderCode: info.shiftOrderCode,
                      },
                      type: 'autoTaskPage',
                      callBack: () => {
                        store.changeData({
                          data: {
                            info: {
                              ...info,
                              containerCode: '',
                            },
                          },
                        });
                      },
                    });
                  }}
                >
                  {t('关箱')}
                </Button>
              </div>
            )
          }
          <Footer
            beforeBack={(back) => {
              store.init();
              back();
            }}
          />
        </div>
      )}
    </IntroContext.Consumer>
  );
}

AutoMovingPage.propTypes = {
  dataLoading: PropTypes.number,
  headerTitle: PropTypes.string,
  baseData: PropTypes.arrayOf(PropTypes.shape()),
  autoPickContainer: PropTypes.string,
  autoPickContainerDisabled: PropTypes.bool,
  autoLocationContainerDisabled: PropTypes.bool,
  info: PropTypes.shape(),
};

export default AutoMovingPage;
