// allDownPage/normalPage 都有用到

import React from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { modal } from '../../../common/index';
import style from '../style.css';
import { orderTypeCodeMap } from '../reducers';

function Info(props) {
  const {
    data,
    orderType,
    orderTypeCode,
  } = props;
  const location = data.location || '';
  return (
    <div>
      <Form>
        <div className={style.firstItem}>
          <span>{data.replenishmentCode}</span>
          <div>
            {t('已拣/总件数')}:
            <span className={style.weight}>
              <span style={{ color: '#ff9636' }}>{data.hadOffNum}</span>
              /{data.goodsTotalNum}
            </span>
          </div>
        </div>
      </Form>
      <Form style={{
        marginTop: '10px',
      }}
      >
        <div className={style.mainInfo}>
          <div className={style.mainItem}>
            <span>{t('库位号')}</span>
            <div style={{ flex: 1, textAlign: 'right' }}>
              <span style={{ fontSize: 16, fontWeight: 'normal' }}>
                {location.substring(0, 6)}
                <b style={{ fontSize: 20 }}>{location.substring(6, 14)}</b>
                <span style={{ color: '#f00' }}>{location.substring(14)}</span>
              </span>
              {
                orderType ? (
                  <button
                    style={{
                      width: '60px',
                      marginLeft: '10px',
                    }}
                    className={style.btn}
                  >{orderType}
                  </button>
                ) : null
              }
            </div>
          </div>
          <div className={style.mainItem}>
            <span>{t('SKC')}</span>
            <div style={{ color: '#FF0000' }}>
              {data.goodsSn?.slice(0, -4) || ''}
              <b style={{ fontSize: 20 }}>{data.goodsSn?.slice(-4) || ''}</b>
            </div>
          </div>
          <div className={classnames(style.mainItem, style.sizeAndColorItem)}>
            {
              data?.color?.length > 6 ?
                (
                  <div
                    className={style.sizeItem}
                    onClick={() => {
                      modal.info({
                        content: <span>{t('颜色')}: {data.color}</span>,
                      });
                    }}
                  >
                    {data.color.slice(0, 6)}
                    <span style={{ color: '#0059ce' }}>...</span>
                  </div>
                ) : (
                  <div className={style.colorItem}>
                    {data.color}
                  </div>
                )
            }
            {
              data?.size?.length > 7 ? (
                <div
                  className={style.sizeItem}
                  onClick={() => {
                    modal.info({
                      content: <span>{t('尺码')}: {data.size}</span>,
                    });
                  }}
                >
                  {data.size.slice(0, 7)}
                  <span style={{ color: '#0059ce' }}>...</span>
                </div>
              ) : (
                <div
                  className={style.sizeItem}
                >
                  {data.size}
                </div>
              )
            }

          </div>
        </div>
      </Form>
    </div>
  );
}

Info.propTypes = {
  data: PropTypes.shape(),
  orderType: PropTypes.string,
  orderTypeCode: PropTypes.number,
};

export default Info;
