// 逐件扫描默认页面，扫描周转箱号
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import LoadMore from 'react-weui/build/packages/components/loadmore';
import {
  UserIntro,
  Footer,
  FocusInput,
  FooterBtn,
  Header,
} from '../../../common/index';
import IntroContext from '../../../user-intro-page/IntroContext';
import BaseData from './base-data';
import store from '../reducers';
import navStore from '../../../nav/reducers';

function DefaultPage(props) {
  const {
    dataLoading,
    baseData,
    boxDisabled,
    headerTitle,
    steps,
    info,
  } = props;
  useEffect(() => {
    navStore.changeData({ data: { pageStore: store } });
  });
  return (
    <IntroContext.Consumer>
      {(context) =>
        // context.changePageStore(store);
        // eslint-disable-next-line implicit-arrow-linebreak
        (
          <div>
            <Header title={headerTitle || t('补货下架')} />
            <UserIntro
              showIntro={context.showIntroVal}
              steps={steps}
              endHandle={() => store.changeData({ data: { status: 'normalPage' } })}
            />
            {
              dataLoading === 0 ?
                <LoadMore loading={dataLoading === 0} /> :
                <BaseData data={baseData} />
            }
            <Form
              style={{ marginTop: '10px' }}
            >
              <FocusInput
                data-step="1"
                data-intro={t('第一步，扫描周转箱号')}
                placeholder={t('请扫描周转箱号')}
                data-bind="containerCode"
                onPressEnter={(e) => {
                  if (e.target.value) {
                    store.scanBox({
                      params: {
                        containerCode: e.target.value,
                        shiftOrderCode: info.shiftOrderCode,
                        replenishmentCode: info.replenishmentCode,
                      },
                    });
                  }
                }}
                disabled={boxDisabled === 0 || context.showIntroVal}
                className="box"
              >
                <label>{t('补货周转箱')}</label>
              </FocusInput>
            </Form>
            <Footer
              beforeBack={() => {
                store.init();
                store.getInfo();
              }}
            >
              <FooterBtn
                onClick={() => store.allDown()}
              >
                {t('整托下架')}
              </FooterBtn>
            </Footer>
          </div>
        )}
    </IntroContext.Consumer>
  );
}

DefaultPage.propTypes = {
  dataLoading: PropTypes.number,
  headerTitle: PropTypes.string,
  steps: PropTypes.arrayOf(PropTypes.shape()),
  baseData: PropTypes.arrayOf(PropTypes.shape()),
  info: PropTypes.shape(),
  boxDisabled: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.number,
  ]),
};

export default DefaultPage;
