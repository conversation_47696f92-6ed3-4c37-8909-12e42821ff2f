// 默认页面，扫描周转箱号
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import LoadMore from 'react-weui/build/packages/components/loadmore';
import {
  <PERSON><PERSON>,
  Header,
  FocusInput,
} from '../../../common/index';
import IntroContext from '../../../user-intro-page/IntroContext';
import BaseData from './base-data';
import store from '../reducers';
import navStore from '../../../nav/reducers';

function AutoMovingPage(props) {
  const {
    dataLoading,
    baseData,
    headerTitle,
  } = props;
  useEffect(() => {
    navStore.changeData({ data: { pageStore: store } });
  });
  return (
    <IntroContext.Consumer>
      {() =>
      // eslint-disable-next-line implicit-arrow-linebreak
        (
          <div>
            <Header title={headerTitle || t('任务领取')} />
            {
            dataLoading === 0 ?
              <LoadMore loading={dataLoading === 0} /> :
              <BaseData data={baseData} />
          }
            <Form>
              <FocusInput
                importance
                data-bind="taskNo"
                placeholder={t('请扫描机器人二维码')}
                className="taskNo"
                onPressEnter={(e) => {
                  if (e.target.value) {
                    const { value } = e.target;

                    store.scanAutoMovingTaskNo({
                      params: {
                        replenishmentCode: value,
                      },
                    });
                  }
                }}
              >
                <label>{t('任务号')}</label>
              </FocusInput>
            </Form>
            <Footer
              beforeBack={(back) => {
                store.init();
                back();
              }}
            />
          </div>
        )}
    </IntroContext.Consumer>
  );
}

AutoMovingPage.propTypes = {
  dataLoading: PropTypes.number,
  headerTitle: PropTypes.string,
  baseData: PropTypes.arrayOf(PropTypes.shape()),
};

export default AutoMovingPage;
