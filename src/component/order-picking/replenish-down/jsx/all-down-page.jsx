// all-down-page 整托下架

import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { classFocus } from '../../../../lib/util';
import Footer from '../../../common/footer';
import FocusInput from '../../../common/focus-input';
import Modal from '../../../common/modal';
import RowInfo from '../../../common/row-info';
import Header from '../../../common/header';
import Info from './Info';
import store from '../reducers';

function AllDownPage(props) {
  const {
    boxNumDisabled,
    locationDisabled,
    palletDisabled,
    containerDisabled,
    info,
    orderType,
    orderTypeCode,
    locationInput,
    validError,
    palletCode,
    nums,
    boxNum,
    containerListBack,
    scanContainerList,
    list,
    headerTitle,
  } = props;
  return (
    <div>
      <Header title={headerTitle || t('补货下架')} />
      <Form>
        <Info data={info} orderType={orderType} orderTypeCode={orderTypeCode} />
        <RowInfo
          extraStyle={{ justifyContent: 'flex-start' }}
          textExtraStyle={{
            width: '32%',
            color: '#FF0000',
            fontWeight: 'bold',
            fontSize: 20,
          }}
          label={t('待拣数量')}
          content={info.waitOffNum}
        />
      </Form>
      <Form
        style={{ marginTop: '10px' }}
      >
        <FocusInput
          autoFocus
          placeholder={t('请扫描')}
          data-bind="locationInput"
          onPressEnter={(e) => {
            if (e.target.value === info.location) {
              store.changeData({
                data: {
                  locationDisabled: 0,
                  palletDisabled: 1,
                },
              });
              classFocus('pallet');
            } else {
              store.changeData({
                data: {
                  locationDisabled: 0,
                },
              });
              Modal.error({
                content: t('下架库位错误'),
                onOk: () => {
                  store.changeData({
                    data: {
                      locationInput: '',
                      locationDisabled: 1,
                    },
                  });
                  classFocus('location');
                },
              });
            }
          }}
          disabled={locationDisabled === 0}
          className="location"
        >
          <label>{t('货位号')}</label>
        </FocusInput>
        <FocusInput
          placeholder={t('请扫描')}
          data-bind="palletCode"
          onPressEnter={(e) => {
            if (e.target.value) {
              store.scanPallet({
                params: {
                  palletCode: e.target.value,
                  location: locationInput,
                },
                info,
              });
            }
          }}
          disabled={palletDisabled === 0}
          className="pallet"
        >
          <label>{t('托盘号')}</label>
        </FocusInput>
        <FocusInput
          placeholder={t('请输入')}
          data-bind="nums"
          disabled={boxNumDisabled === 0 || validError === 2}
          className="boxNum"
          onPressEnter={() => {
            if (nums === boxNum) {
              store.changeData({
                data: {
                  validError: 0,
                  boxNumDisabled: 0,
                },
              });
              store.doDownPallet({
                params: {
                  list,
                  location: locationInput,
                  palletCode,
                  orderType,
                  replenishmentCode: info.replenishmentCode,
                  shiftOrderCode: info.shiftOrderCode,
                },
                info,
              });
            } else if (validError !== 2) {
              if (validError === 1) {
                store.changeData({
                  data: {
                    containerListBack: list.slice(),
                    nums: 0,
                    validError: validError + 1,
                    boxNumDisabled: 0,
                  },
                });
                Modal.error({
                  content: t('数量输入错误,请扫描补货周转箱进行校验'),
                  onOk: () => {
                    classFocus('box');
                  },
                });
              } else {
                store.changeData({
                  data: {
                    nums: '',
                    validError: validError + 1,
                    boxNumDisabled: 0,
                  },
                });
                Modal.error({
                  content: t('数量输入错误，请重新输入'),
                  onOk: () => {
                    store.changeData({
                      data: {
                        boxNumDisabled: 1,
                      },
                    });
                    classFocus('boxNum');
                  },
                });
              }
            } else {
              store.changeData({
                data: {
                  nums: '',
                },
              });
              Modal.error({
                content: t('请扫描托盘'),
                onOk: () => {
                  classFocus('pallet');
                },
              });
            }
          }}
        >
          <label>{t('箱数')}</label>
        </FocusInput>
        {
          validError === 2 ? (
            <FocusInput
              data-bind="containerCode"
              placeholder={t('请扫描')}
              disabled={containerDisabled}
              className="box"
              onPressEnter={(e) => {
                if (e.target.value) {
                  store.reScanContainer({
                    params: {
                      containerCode: e.target.value,
                    },
                    containerListBack,
                    scanContainerList,
                    location: locationInput,
                    palletCode,
                    info,
                    list,
                    nums,
                    orderType,
                    replenishmentCode: info.replenishmentCode,
                    shiftOrderCode: info.shiftOrderCode,
                  });
                }
              }}
            >
              <label>{t('补货周转箱')}</label>
            </FocusInput>
          ) : null
        }
      </Form>
      <Footer
        beforeBack={() => {
          store.init();
          store.getInfo();
        }}
      />
    </div>
  );
}

AllDownPage.propTypes = {
  headerTitle: PropTypes.string,
  orderType: PropTypes.string,
  orderTypeCode: PropTypes.number,
  info: PropTypes.shape(),
  list: PropTypes.arrayOf(PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.shape(),
  ])),
  boxNumDisabled: PropTypes.number,
  locationDisabled: PropTypes.number,
  palletDisabled: PropTypes.number,
  containerDisabled: PropTypes.bool,
  locationInput: PropTypes.string,
  validError: PropTypes.number,
  palletCode: PropTypes.string,
  nums: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  boxNum: PropTypes.number,
  containerListBack: PropTypes.arrayOf(PropTypes.shape()),
  scanContainerList: PropTypes.arrayOf(PropTypes.shape()),
};

export default AllDownPage;
