// 默认页面，扫描周转箱号
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import LoadMore from 'react-weui/build/packages/components/loadmore';
import {
  Cells, Cell, CellBody,
} from 'react-weui/build/packages/components/cell';
import {
  Footer,
  Header,
  pages,
} from 'common/index';
import IntroContext from '../../../user-intro-page/IntroContext';
import BaseData from './base-data';
import store from '../reducers';
import navStore from '../../../nav/reducers';

const { View } = pages;

function DefaultPage(props) {
  const {
    dataLoading,
    baseData,
    headerTitle,
    showHistoryPage,
  } = props;
  useEffect(() => {
    navStore.changeData({ data: { pageStore: store } });
  });
  return (
    <IntroContext.Consumer>
      {() => (
        <div>
          <Header title={headerTitle || t('补货下架')}>
            {showHistoryPage || (
              <div
                onClick={() => {
                  store.getHistory();
                }}
              >
                {t('历史')}
              </div>
            )}
          </Header>

          <View diff={100}>
            {
              dataLoading === 0 ?
                <LoadMore loading={dataLoading === 0} /> :
                <BaseData data={baseData} />
            }
            <Cells>
              <Cell
                onClick={() => {
                  if (dataLoading === 0) {
                    return;
                  }
                  store.getUnderTask();
                }}
              >
                <CellBody>
                  {t('领取下架任务')}
                </CellBody>
              </Cell>
              <Cell
                onClick={() => {
                  if (dataLoading === 0) {
                    return;
                  }
                  store.obtainAutoUnderTask();
                }}
              >
                <CellBody>
                  {t('领取自动化库区下架任务')}
                </CellBody>
              </Cell>
            </Cells>
          </View>
          <Footer
            beforeBack={(back) => {
              store.init();
              back();
            }}
          />
        </div>
      )}
    </IntroContext.Consumer>
  );
}

DefaultPage.propTypes = {
  dataLoading: PropTypes.number,
  headerTitle: PropTypes.string,
  baseData: PropTypes.arrayOf(PropTypes.shape()),
  showHistoryPage: PropTypes.bool,
};

export default DefaultPage;
