// container-page 整箱下架

import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import LoadMore from 'react-weui/build/packages/components/loadmore';

import { classFocus } from '../../../../lib/util';
import {
  Header,
  FooterBtn,
  UserIntro,
  Footer,
  FocusInput,
  modal,
} from '../../../common/index';
import IntroContext from '../../../user-intro-page/IntroContext';
import BaseData from './base-data';
import store, { orderTypeCodeMap } from '../reducers';
import style from '../style.css';

function ContainerPage(props) {
  const {
    dispatch,
    steps,
    info,
    dataLoading,
    baseData,
  } = props;

  const location = info.location || ''; // 库位号

  // handle 短拣
  const handleClickShortPick = () => {
    modal.confirm({
      content: (
        <table width="100%">
          <tbody>
            <tr>
              <td colSpan={2}>
                {t('短拣明细如下,是否继续?')}
              </td>
            </tr>
            <tr>
              <td width="30%" align="right">
                {t('库位号')} :
              </td>
              <td align="left">
                {info.location || ''}
              </td>
            </tr>
            <tr>
              <td width="30%" align="right">
                {t('周转箱号')} :
              </td>
              <td align="left">
                {info.containerCode || ''}
              </td>
            </tr>
            <tr>
              <td width="30%" align="right">
                {t('件数')} :
              </td>
              <td align="left">
                {info.waitOffNum || ''}
              </td>
            </tr>
          </tbody>
        </table>
      ),
      okText: t('确认'),
      cancelText: t('取消'),
      onOk: () => {
        store.shortPickByBox({
          params: {
            containerCode: info.containerCode,
            replenishmentCode: info.replenishmentCode,
            shiftOrderCode: info.shiftOrderCode,
            location: info.location,
          },
          dispatch,
        });
      },
    });
  };

  // handle 明细
  const handleClickDetail = () => {
    store.getDetail({
      params: {
        shiftOrderCode: info.shiftOrderCode,
        replenishmentCode: info.replenishmentCode,
        underWay: info.underWay,
      },
    });
  };

  return (
    <IntroContext.Consumer>
      {(context) => {
        context.changePageStore(store);
        return (
          <div>
            <Header title={t('补货整箱下架')}>
              <div
                style={{ padding: '0 10px', color: '#fff' }}
                onClick={handleClickShortPick}
              >
                {t('短拣')}
              </div>
            </Header>
            <UserIntro
              showIntro={context.showIntroVal}
              steps={steps}
              finishHandle={() => {
                store.changeData({ data: { status: 'containerPage' } });
                classFocus('containerCodeInputByBox');
              }}
            />
            <Form>
              <Form>
                <div className={style.firstItem}>
                  <span>{info.replenishmentCode}</span>
                  <div>
                    {t('已拣/总件数')}:
                    <span className={style.weight}>
                      <span style={{ color: '#ff9636' }}>{info.hadOffNum}</span>
                      /{info.goodsTotalNum}
                    </span>
                  </div>
                </div>
              </Form>
              {
                dataLoading === 0 ?
                  <LoadMore loading={dataLoading === 0} /> :
                  <BaseData data={baseData} />
              }
              <div className={style.mainInfo}>
                <div className={style.mainItem}>
                  <span>{t('库位号')}</span>
                  <div style={{ flex: 1, textAlign: 'right' }}>
                    <span style={{ fontSize: 16, fontWeight: 'normal' }}>
                      {location.substring(0, 6)}
                      <b style={{ fontSize: 20 }}>{location.substring(6, 14)}</b>
                      <span style={{ color: '#f00' }}>{location.substring(14)}</span>
                    </span>
                    <button
                      style={{
                        width: '60px',
                        marginLeft: '10px',
                      }}
                      className={style.btn}
                    >{
                        info.orderTypeCode === orderTypeCodeMap.get(t('紧急')) ?
                          t('紧急整箱') :
                          t('日常整箱')
                      }
                    </button>
                  </div>
                </div>
                <div className={style.mainItem}>
                  <span>{t('周转箱')}</span>
                  <div style={{ color: '#FF0000' }}>
                    {info.containerCode || ''}
                  </div>
                </div>
              </div>
              <FocusInput
                importance
                data-bind="containerCodeInputByBox"
                placeholder={t('请扫描周转箱号')}
                className="boxByBox"
                onPressEnter={(e) => {
                  if (e.target.value) {
                    const { value } = e.target;

                    if (value !== info.containerCode) {
                      modal.error({
                        content: t('周转箱扫描错误'),
                        onOk: () => {
                          store.changeData({
                            data: {
                              containerCodeInputByBox: '',
                            },
                          });
                          classFocus('boxByBox');
                        },
                      });
                      return;
                    }

                    store.scanContainerByBox({
                      params: {
                        containerCode: value,
                        replenishmentCode: info.replenishmentCode,
                        shiftOrderCode: info.shiftOrderCode,
                        location: info.location,
                      },
                      dispatch,
                    });
                  }
                }}
              >
                <label>{t('周转箱号')}</label>
              </FocusInput>
            </Form>
            <Footer
              beforeBack={() => {
                store.init();
                store.getInfo();
              }}
            >
              <FooterBtn
                type="primary"
                onClick={handleClickDetail}
              >
                {t('明细')}
              </FooterBtn>
            </Footer>
          </div>
        );
      }}
    </IntroContext.Consumer>
  );
}

ContainerPage.propTypes = {
  dispatch: PropTypes.func.isRequired,
  steps: PropTypes.arrayOf(PropTypes.shape()),
  info: PropTypes.shape(),
  baseData: PropTypes.arrayOf(PropTypes.shape()),
  dataLoading: PropTypes.number,
};

export default ContainerPage;
