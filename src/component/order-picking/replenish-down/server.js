import { sendPostRequest } from '../../../lib/public-request';

// 下架任务查询
export const queryTask = (param = {}) => sendPostRequest({
  url: '/replenish_shelves/query_task',
  param,
}, process.env.WWS_URI);

// 查询当前用户排名
export const pdaQueryRankInfo = (param) => sendPostRequest({
  baseUrl: process.env.WKB,
  url: '/rank/query',
  param,
});

// 扫描周转箱
export const scanPickContainer = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_pick_container',
  param,
}, process.env.WWS_URI);

// 扫描库位
export const scanLocation = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_location',
  param,
}, process.env.WWS_URI);

// 关箱
export const closePickContainer = (param) => sendPostRequest({
  url: '/replenish_shelves/close_pick_container',
  param,
}, process.env.WWS_URI);

// 扫描商品条码
export const scanGoods = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_goods',
  param,
}, process.env.WWS_URI);

/**
 * 获取整托下架的任务
 */
export const downPallet = (param) => sendPostRequest({
  url: '/replenish_shelves/bottom_shelves',
  param,
}, process.env.WWS_URI);

// 扫描托盘号
export const scanPallet = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_pallet',
  param,
});

// 进行整托下架
export const doDownPallet = (param) => sendPostRequest({
  url: '/replenish_shelves/under_pallet',
  param,
});

// 短拣
export const shortPick = (param) => sendPostRequest({
  url: '/replenish_shelves/short_pick',
  param,
}, process.env.WWS_URI);

// 获取下架信息
export const getDetailApi = (param) => sendPostRequest({
  url: '/replenish_shelves/under_info',
  param,
}, process.env.WWS_URI);

// 整箱下架-领取下架任务
export const queryUnderTaskAPI = (param = {}) => sendPostRequest({
  url: '/replenish_shelves/obtain_under_task',
  param,
}, process.env.WWS_URI);

// 整箱下架-短拣
export const shortPickByBoxAPI = (param = {}) => sendPostRequest({
  url: '/replenish_shelves/short_pick_by_box',
  param,
}, process.env.WWS_URI);

// 整箱下架-扫描周转箱
export const scanPickContainerByBoxAPI = (param = {}) => sendPostRequest({
  url: '/replenish_shelves/scan_pick_container_by_box',
  param,
}, process.env.WWS_URI);

/**
 * 补货下架 - 历史查询
 * @param param
 * @returns {*}
 */
export const getHistoryApi = (param) => sendPostRequest({
  url: '/scan_down/history_down_list',
  param,
}, process.env.WWS_URI);

// 领取下架任务(炬星自动搬运项目
export const obtainUnderTaskAutoMovingAPI = (param) => sendPostRequest({
  url: '/replenish_shelves/obtain_under_task_auto_moving',
  param,
}, process.env.WWS_URI);

// 海柔自动化空箱处理
export const autoEmptyBoxAPI = (param) => sendPostRequest({
  url: '/replenish_shelves/auto_empty_box',
  param,
}, process.env.WWS_URI);

// 自动化库区领取下架任务
export const obtainAutoUnderTaskAPI = (param) => sendPostRequest({
  url: '/replenish_shelves/obtain_auto_under_task',
  param,
}, process.env.WWS_URI);

// 自动化库区下架扫描拣货容器号
export const autoScanPickContainerAPI = (param) => sendPostRequest({
  url: '/replenish_shelves/auto_scan_pick_container',
  param,
}, process.env.WWS_URI);

// 自动化库区下架扫描货位周转箱号
export const autoScanLocationContainerAPI = (param) => sendPostRequest({
  url: '/replenish_shelves/auto_scan_location_container',
  param,
}, process.env.WWS_URI);
