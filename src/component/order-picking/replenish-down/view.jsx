/* eslint-disable react/jsx-props-no-spreading */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import Initpage from './jsx/init-page';
import DefaultPage from './jsx/default';
import AllDownPage from './jsx/all-down-page';
import NormalPage from './jsx/normal-page';
import DetailPage from './jsx/detail-page';
import ContainerPage from './jsx/container-page';
import HistoryPage from './jsx/history';
import AutoMovingPage from './jsx/auto-moving-page';
import AutoTaskPage from './jsx/auto-task-page';

import store from './reducers';

class Container extends Component {
  componentDidMount() {
    store.getInfo();
  }

  render() {
    const {
      status,
      info,
      showDetail,
      detailList,
      dispatch,
      showHistoryPage,
    } = this.props;
    let children;
    switch (status) {
      case 'initPage':
        children = (<Initpage {...this.props} />);
        break;
      case 'default': // 默认 扫描周转箱
        children = (<DefaultPage {...this.props} />);
        break;
      case 'allDownPage': // 整托下架
        children = (<AllDownPage {...this.props} />);
        break;
      case 'normalPage': // 逐件下架
        children = (<NormalPage {...this.props} />);
        break;
      case 'containerPage': // 整箱下架
        children = (<ContainerPage {...this.props} />);
        break;
      case 'autoMovingPage': // 炬星整箱下架
        children = (<AutoMovingPage {...this.props} />);
        break;
      case 'autoTaskPage': // 自动化库区
        children = (<AutoTaskPage {...this.props} />);
        break;
      default:
        break;
    }

    if (showHistoryPage) {
      return <HistoryPage {...this.props} />;
    }

    if (showDetail) {
      children = (
        <DetailPage
          data={detailList}
          dispatch={dispatch}
          info={info}
        />
      );
    }

    return (
      <div style={{ marginBottom: '56px' }}>
        {children}
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  showDetail: PropTypes.bool,
  detailList: PropTypes.arrayOf(PropTypes.arrayOf(PropTypes.shape())),
  info: PropTypes.shape(),
  status: PropTypes.string,
  showHistoryPage: PropTypes.bool,
};

export default i18n(Container);
