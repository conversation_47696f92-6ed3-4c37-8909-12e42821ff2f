import React from 'react';
import { t } from '@shein-bbl/react';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import Modal from 'common/modal';
import { LOCAL_REPLENISH_DOWN_PICKER } from 'lib/storage';
import {
  queryTask,
  pdaQueryRankInfo,
  scanPickContainer,
  scanLocation,
  closePickContainer,
  scanGoods,
  downPallet,
  shortPick,
  doDownPallet,
  scanPallet,
  getDetailApi,
  queryUnderTaskAPI,
  shortPickByBoxAPI,
  scanPickContainerByBoxAPI,
  getHistoryApi,
  obtainUnderTaskAutoMovingAPI,
  autoEmptyBoxAPI,
  obtainAutoUnderTaskAPI,
  autoScanPickContainerAPI,
  autoScanLocationContainerAPI,
} from './server';
import { getApolloConfigAPI } from '../../../server/basic/common';
import { modal, message } from '../../common/index';
import { classFocus, getHeaderTitle, apolloFormatObj } from '../../../lib/util';

export const orderTypeCodeMap = new Map([
  [t('紧急'), 1],
  [t('日常'), 2],
]);
export const underWayMap = new Map([
  [t('逐件'), 0],
  [t('整箱'), 1],
  [t('炬星'), 3], // 炬星下架
]);

const defaultState = {
  status: 'initPage', // default 首页/allDownPage 整托下架/normalPage 逐件下架/containerPage 整箱下架
  headerTitle: '',
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  steps: [],
  info: '', // 任务信息

  containerCode: '', // 外层周转箱
  orderType: '', // 补货单据类型 1.紧急补货 2.日常补货
  orderTypeCode: '', // 补货单据类型code 1.紧急补货 2.日常补货
  locationInput: '', // 货位号/库位号

  // default
  boxDisabled: 1,

  // base-data
  baseData: [
    {
      label: t('待领取任务数'),
      num: 0,
      type: 'info',
    },
    {
      label: t('已下架件数'),
      num: 0,
      type: 'warn',
    },
    {
      label: t('排名'),
      num: 0,
      type: 'sky',
    },
  ],

  // detail-page 明细
  showDetail: false,
  detailList: [[], []],

  // normal-page 逐件下架
  goodsSnPrint: '', // 商品条码
  containerDisabled: false, // 周转箱 input disabled
  locationDisabled: 1, // 库位号 input disabled
  barCodeDisabled: 1, // 商品条码 input disabled
  shortNum: 0, // 输入短拣数量
  shortPickType: 0, // 1为紧急任务且配置为逐件扫描
  countNumModal: false, // 点数下架弹窗
  countNumVal: '', // 点数值
  waitOffNumInput: 0, // 待下数 input

  // all-down-page 整托下架
  palletCode: '', // 托盘号
  palletDisabled: 0, // 托盘号 input disabled
  boxNum: 0, // 扫托盘号 箱数
  list: [], // 扫托盘号
  boxNumDisabled: 0, // 箱数 input disabled
  validError: 0,
  nums: '',
  containerListBack: [],
  scanContainerList: [],

  // container-page 整箱下架
  containerCodeInputByBox: '', // 整箱下架 扫描周转箱

  historyList: [], // 历史数据
  showHistoryPage: false, // 展示历史页面
  selectShow: false,
  pickerDataList: [{
    items: [
      { label: t('待拣数量'), value: 0 },
      { label: t('已拣数量/总数量'), value: 1 },
    ],
  }],
  currentPicker: 0,
  currentLabel: t('待拣数量'),

  taskNo: '', // 炬星整箱下架
  degradeScanUpgrade: false, // 降级开关

  // auto-task-page 自动化库区页面
  autoPickContainer: '', // 周转箱
  autoPickContainerDisabled: true, // 周转箱 input disabled
  autoLocationContainer: '', // 库位周转箱
  autoLocationContainerDisabled: true, // 库位周转箱 input disabled
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    Object.assign(draft, action.data);
  },
  // 初始当前页面数据
  resetPageStore(draft) {
    Object.assign(draft, defaultState, {
      headerTitle: draft.headerTitle,
      baseData: draft.baseData,
      boxDisabled: false,
    });
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  * reset(action, ctx) {
    const { degradeScanUpgrade } = yield '';
    yield ctx.changeData({
      data: {
        ...defaultState,
        status: 'initPage',
        headerTitle: getHeaderTitle(),
        degradeScanUpgrade,
      },
    });
    yield ctx.getInfo();
  },
  // 获取页面降级开关 DEGRADE_REPLENISH_DOWN
  * getDegradeConfig(action, ctx) {
    try {
      const res = yield getApolloConfigAPI({ params: ['DEGRADE_REPLENISH_DOWN'] });
      const apolloConfigFormatRes = apolloFormatObj(res.info);

      if (res.code === '0' && apolloConfigFormatRes?.DEGRADE_REPLENISH_DOWN === '1') {
        yield ctx.changeData({ data: { degradeScanUpgrade: apolloConfigFormatRes?.DEGRADE_REPLENISH_DOWN === '1' } });
      } else {
        // 获取排名。后端接口降级
        yield ctx.getRankInfo();
      }
    } catch (e) {
      console.error(e);
    }
  },
  // 下架任务查询&查询当前用户排名
  * getInfo(action, ctx, put) {
    // 获取页面降级开关
    yield ctx.getDegradeConfig();
    // 清空历史页面状态
    yield ctx.changeData({
      data: {
        showHistoryPage: false,
      },
    });
    markStatus('dataLoading');
    const queryTaskRes = yield queryTask();
    if (queryTaskRes.code === '0') {
      yield put((draft) => {
        draft.baseData[0].num = queryTaskRes.info.waitTaskNum;
        draft.baseData[1].num = queryTaskRes.info.downNum;
        draft.boxDisabled = false;
      });
      classFocus('box');
    } else {
      modal.error({
        content: queryTaskRes.msg || t('请求数据出错'),
        className: 'box',
      });
    }
  },
  // 获取排名
  * getRankInfo(action, ctx, put) {
    // 如果开了降级，就不调接口
    const { degradeScanUpgrade } = yield '';
    if (degradeScanUpgrade) { return; }
    const queryRankRes = yield pdaQueryRankInfo({ rankTypeCode: 2 });
    if (queryRankRes.code === '0') {
      yield put((draft) => {
        draft.baseData[2].num = `${queryRankRes.info.rank}/${queryRankRes.info.total}`;
      });
      classFocus('box');
    } else {
      modal.error({
        content: queryRankRes.msg || t('请求数据出错'),
      });
    }
  },
  // 领取下架任务
  * getUnderTask(action, ctx, put) {
    markStatus('dataLoading');
    const { code, info, msg } = yield queryUnderTaskAPI();
    if (code === '0') {
      if (Number(info.hintContainerClose) === 1) {
        const status = yield new Promise((r) => modal.confirm({
          content: t('存在未关箱的作业中周转箱{}，是否要关箱？', info.containerCode || '-'),
          onOk: () => r(1),
          onCancel: () => r(2),
          okText: t('确认'),
          cancelText: t('取消'),
        }));
        if (status === 1) {
          yield ctx.closeBox({
            params: {
              containerCode: info.containerCode,
              shiftOrderCode: info.shiftOrderCode,
              hintContainerClose: 1,
            },
          });
          return;
        }
      }

      switch (info.underWay) {
        case underWayMap.get(t('整箱')):
          yield put((draft) => {
            draft.status = 'containerPage';
            draft.containerCodeInputByBox = '';
          });
          classFocus('boxByBox');
          break;
        case underWayMap.get(t('炬星')):
          yield put((draft) => {
            draft.status = 'autoMovingPage';
            draft.taskNo = '';
          });
          classFocus('taskNo');
          break;
        default:
          yield put((draft) => {
            draft.status = 'default';
            draft.containerCode = '';
          });
          classFocus('containerCode');
          break;
      }
      yield put((draft) => {
        draft.info = info;
      });
    } else {
      yield put((draft) => {
        draft.info = {};
        draft.status = 'initPage';
      });
      yield new Promise((r) => modal.error({
        content: msg,
        onOk: () => r(1),
      }));
    }
  },
  * scanBox(action, ctx, put) {
    markStatus('boxDisabled');
    const res = yield scanPickContainer(action.params);
    if (res.code !== '0') {
      yield put((draft) => {
        draft.containerCode = '';
      });
      modal.error({
        content: res.msg,
        onOk: () => classFocus('box'),
      });
    } else {
      // 进入normalPage页面时，判断是否已发起过短拣
      const {
        waitOffNum, goodsTotalNum, changeArea, floor, area,
      } = res.info;
      if ((waitOffNum !== goodsTotalNum) && (String(res.info.type) !== '1')) {
        message.warning(`${t('当前任务未完成，需重新点数{}件', waitOffNum)}`);
      }
      if (changeArea) {
        modal.info({
          // eslint-disable-next-line react/jsx-filename-extension
          content: <span>{t('任务切换为{}层{}区', floor, <b style={{ color: 'red' }}>{area}</b>)}</span>,
        });
      }
      const picker = JSON.parse(localStorage.getItem(LOCAL_REPLENISH_DOWN_PICKER));
      if (picker) {
        yield put((draft) => {
          draft.currentPicker = picker.value;
          draft.currentLabel = picker.label;
        });
      }
      yield put((draft) => {
        draft.info = res.info;
        draft.status = 'normalPage';
        draft.orderType = res.info.orderType;
        draft.orderTypeCode = res.info.orderTypeCode || '';
        draft.containerDisabled = true;
        draft.containerCode = res.info.containerCode;
        draft.shortPickType = res.info.type;
        draft.waitOffNumInput = res.info.waitOffNum;
      });
      if (action.params.containerCode !== res.info.containerCode) {
        modal.success({
          content: t('您存在未完成的任务'),
          onOk: () => classFocus('location'),
        });
      } else {
        classFocus('location');
      }
    }
  },
  * scanLocation(action, ctx, put) {
    yield put((draft) => {
      draft.locationDisabled = 0;
    });
    const res = yield scanLocation(action.params);
    if (res.code === '503801') {
      // 重新领取新的补货任务
      const status = yield new Promise((r) => modal.error({
        content: res.msg,
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
        yield ctx.reset();
      }
    } else if (res.code !== '0') {
      yield put((draft) => {
        draft.locationDisabled = 1;
        draft.locationInput = '';
      });
      modal.error({
        content: res.msg,
        onOk: () => classFocus('location'),
      });
    } else {
      // 关箱
      if (res.info.type === 2) {
        yield put((draft) => {
          draft.locationDisabled = 1;
        });
        const status = yield new Promise((r) => modal.confirm({
          content: t('确定是否关箱?'),
          onOk: () => r(1),
          onCancel: () => r(2),
        }));
        if (status === 1) {
          yield ctx.closeBox({
            params: {
              containerCode: action.params.containerCode,
              shiftOrderCode: action.shiftOrderCode,
            },
            names: {
              name: 'locationInput',
              disabled: 'locationDisabled',
              className: 'location',
            },
          });
        }
        if (status === 2) {
          yield ctx.changeData({
            data: {
              locationInput: '',
              locationDisabled: 1,
            },
          });
          classFocus('location');
        }
      } else if (res.info.type === 3) {
        modal.success({
          content: t('请注意, 该货位需要点数下架{}件', action.waitOffNum),
          onOk: () => {
            if (res.info.replenishmentCursorMode && res.info.replenishmentCursorMode === 1) {
              classFocus('waitOffNum');
            } else {
              classFocus('barCode');
            }
          },
        });
      } else if (res.info.replenishmentCursorMode && res.info.replenishmentCursorMode === 1) {
        classFocus('waitOffNum');
      } else {
        classFocus('barCode');
      }
    }
  },
  * closeBox(action, ctx, put) {
    markStatus('dataLoading');
    const res = yield closePickContainer(action.params);

    // 自动化库区关箱
    if (action?.type === 'autoTaskPage') {
      if (res.code === '0') {
        // 提示推荐上架园区
        let recommendUpperParkName = res.info?.recommendUpperParkName || '';
        recommendUpperParkName = recommendUpperParkName ? `，${t('推荐上架园区')}：${recommendUpperParkName}` : '';
        message.success(t('关箱成功{}', recommendUpperParkName));
        action?.callBack();
      } else {
        modal.error({
          content: res.msg,
        });
      }
    } else {
      if (res.code !== '0') {
        // action下如果存在names对象
        if (action.names) {
          yield put((draft) => {
            draft[action.names.name] = '';
            draft[action.names.disabled] = 1;
          });
          modal.error({
            content: res.msg,
            onOk: classFocus(action.names.className),
          });
        }
      } else {
        yield put((draft) => {
          draft.containerCode = '';
          draft.containerDisabled = false;
          draft.locationInput = '';
          draft.locationDisabled = 1;
          draft.goodsSnPrint = '';
          draft.barCodeDisabled = 1;
          draft.waitOffNumInput = 0;
        });
        classFocus('box');
        let recommendUpperParkName = res.info?.recommendUpperParkName || '';
        recommendUpperParkName = recommendUpperParkName ? `，${t('推荐上架园区')}：${recommendUpperParkName}` : '';
        message.success(t('关箱成功{}', recommendUpperParkName));
      }
    }
  },
  // 扫描商品条码
  * scanGoods(action, ctx, put) {
    markStatus('barCodeDisabled');
    yield put((draft) => {
      draft.goodsSnPrint = '';
    });
    const res = yield scanGoods(action.params);
    if (res.code === '503801') {
      // 重新领取新的补货任务
      const status = yield new Promise((r) => modal.error({
        content: res.msg,
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
        yield ctx.reset();
      }
    } else if (res.code !== '0') {
      if (res.code === '503002') {
        const status = yield new Promise((r) => modal.confirm({
          content: t('当前扫描商品，在周转箱上已经存在，不允许混放，请关箱！'),
          onOk: () => r(1),
          onCancel: () => r(2),
          okText: t('关箱'),
          cancelText: t('返回'),
        }));
        if (status === 1) {
          yield ctx.closeBox({
            params: {
              containerCode: action.params.containerCode,
              shiftOrderCode: action.params.shiftOrderCode,
            },
            names: {
              name: 'goodsSnPrint',
              disabled: 'barCodeDisabled',
              className: 'barCode',
            },
          });
        }
        // 1. 清空库位,条码
        // 2. 光标定位到库位
        if (status === 2) {
          yield put((draft) => {
            draft.locationInput = '';
            draft.locationDisabled = 1;
            draft.goodsSnPrint = '';
          });
          classFocus('location');
        }
        return;
      }
      modal.error({
        content: res.msg,
        onOk: classFocus('barCode'),
      });
    } else {
      if (res.info?.showPopupToTakeTheBox) {
        yield new Promise((r) => modal.info({
          content: t('库位周转箱库存为空请取走库位周转箱如有实物请进行盘盈?'),
          onOk: () => r('ok'),
        }));
      }

      if (res.info.type === 2) { // 关箱
        // 明细更新
        if (res.info.taskGoodsInfo) {
          yield put((draft) => {
            draft.info = { ...action.info, ...res.info.taskGoodsInfo };
          });
        }

        const status = yield new Promise((r) => modal.confirm({
          content: t('确定是否关箱?'),
          onOk: () => r(1),
          onCancel: () => r(2),
        }));
        if (status === 1) {
          yield ctx.closeBox({
            params: {
              containerCode: action.params.containerCode,
              shiftOrderCode: action.params.shiftOrderCode,
            },
            names: {
              name: 'goodsSnPrint',
              disabled: 'barCodeDisabled',
              className: 'barCode',
            },
          });
        }
        if (status === 2) {
          classFocus('barCode');
        }
      } else if (res.info.type === 3) { // 任务已完成
        const { autoFlag } = action.info || {};
        const msgContent = (
          <>
            {t('任务已结束')}
            {
              ![null, ''].includes(res.info.priority) &&
              (<span>,{t('任务优先级')}-<span style={{ color: 'red' }}>{res.info.priority}</span></span>)
            }
            {
              // eslint-disable-next-line no-nested-ternary
              res.info.recommendUpperParkName ?
                (<span>,{t('推荐上架园区')}: {res.info.recommendUpperParkName}</span>) :
                (autoFlag ? <span>,{t('点击确定返回自动化库区下架任务领取页面')}</span> : <span>,{t('点击确定返回任务领取页面')}</span>)
            }
            {
              Number(res.info.recommendedTasksRsp?.isAcrossArea) === 1 &&
              (<span>{`, ${t('任务将由上一个任务的{}库区转移到{}库区', res.info.recommendedTasksRsp.currentArea, res.info.recommendedTasksRsp.area)}`}</span>)
            }
          </>
        );

        const status = yield new Promise((r) => modal.success({
          content: msgContent,
          okText: t('确定'),
          onOk: () => r(1),
        }));
        if (status === 1) {
          if (autoFlag) {
            yield ctx.reset();
            yield this.obtainAutoUnderTask();
          } else {
            yield ctx.reset();
          }
        }
      } else {
        // 明细更新
        if (res.info.taskGoodsInfo) {
          yield put((draft) => {
            draft.info = { ...action.info, ...res.info.taskGoodsInfo };
          });
        }

        // const {
        //   goodsSn,
        // } = res.info.taskGoodsInfo;

        // itemCount > 0 需要关箱
        const bool1 = res.info.type === 1 && res.info.itemCount > 0;
        // 新增补货下架交互-当下架成功如果下一条商品与当前下架不一致时，则进行提示关箱  当选择“是”时则自动关箱，选择“否”时，则不进行任何改变
        // const bool2 = res.info.type === 1 && action.params.goodsSn !== goodsSn;
        // if (bool1 || bool2) {
        if (bool1) {
          const status = yield new Promise((r) => modal.confirm({
            content: t('确定是否关箱?'),
            onOk: () => r(1),
            onCancel: () => r(2),
          }));
          if (status === 1) {
            yield ctx.closeBox({
              params: {
                containerCode: action.params.containerCode,
                shiftOrderCode: action.params.shiftOrderCode,
              },
              names: {
                name: 'goodsSnPrint',
                disabled: 'barCodeDisabled',
                className: 'barCode',
              },
            });
          }
          if (status === 2) {
            classFocus('barCode');
          }
        } else {
          if (res.info.status === 3) {
            // status:3 下架中
            if (res.info.isChangeBox === true) {
              yield put((draft) => {
                draft.containerCode = '';
                draft.containerDisabled = false;
                draft.info = { ...action.info, containerCode: '' };
              });
              classFocus('box');
            } else {
              // 下架中时清空条码
              classFocus('barCode');
            }

            yield put((draft) => {
              draft.info = { ...action.info, ...res.info.taskGoodsInfo };
              draft.waitOffNumInput = res.info.taskGoodsInfo.waitOffNum || action.info.waitOffNum;
            });
          } else {
            // status:0 待下架, status:1 已下架, status:2 短拣
            if (res.info.status === 1 && res.info.isChangeBox === true) {
              yield put((draft) => {
                draft.containerCode = '';
                draft.containerDisabled = false;
                draft.locationInput = '';
                draft.locationDisabled = 1;
                draft.info = { ...action.info, ...res.info.taskGoodsInfo, containerCode: '' };
              });
              classFocus('box');
            } else {
              yield put((draft) => {
                draft.locationInput = '';
                draft.locationDisabled = 1;
                draft.info = { ...action.info, ...res.info.taskGoodsInfo };
              });
              classFocus('location');
            }
            yield put((draft) => {
              draft.waitOffNumInput = res.info.taskGoodsInfo.waitOffNum || action.info.waitOffNum;
            });
            message.success(t('下架成功'));
          }
        }
      }
    }
  },
  // 短拣
  * shortPick(action, ctx, put) {
    const res = yield shortPick(action.params);
    if (res.code === '503801') {
      // 重新领取新的补货任务
      const status = yield new Promise((r) => modal.error({
        content: res.msg,
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
        yield ctx.reset();
      }
    } else if (res.code !== '0') {
      yield put((draft) => {
        draft.locationInput = '';
        draft.locationDisabled = 1;
        draft.containerCode = '';
        draft.containerDisabled = false;
        draft.barCodeDisabled = 1;
        draft.goodsSnPrint = '';
        draft.palletCode = '';
        draft.palletDisabled = 1;
        draft.validError = 0;
        draft.containerListBack = [];
        draft.scanContainerList = [];
        draft.waitOffNumInput = 0;
      });
      modal.error({
        content: res.msg,
      });
    } else {
      if (res.info?.isEmpty) {
        const emptyContent = (
          <>
            <div>{t('由于箱空，移位单已结束')}</div>
            <div>{t('请点击关箱更换新周转箱')}</div>
          </>
        );
        const status = yield new Promise((r) => modal.success({
          content: emptyContent,
          okText: t('确定'),
          onOk: () => r(1),
        }));
        if (status === 1) {
          yield ctx.init();
          yield ctx.reset();
        }
        return;
      }
      if (res.info.type === 2) { // 完成
        const msgContent = (
          <>
            {t('短拣成功！推荐上架园区: {}', res.info.recommendUpperParkName)}
            {
              ![null, ''].includes(res.info.priority) &&
              (<span>,{t('任务优先级')}-<span style={{ color: 'red' }}>{res.info.priority}</span></span>)
            }
            {
              Number(res.info.recommendedTasksRsp?.isAcrossArea) === 1 &&
              (<span>{`, ${t('任务将由上一个任务的{}库区转移到{}库区', res.info.recommendedTasksRsp.currentArea, res.info.recommendedTasksRsp.area)}`}</span>)
            }
          </>
        );

        const status = yield new Promise((r) => modal.success({
          content: msgContent,
          okText: t('确定'),
          onOk: () => r(1),
        }));
        if (status === 1) {
          yield ctx.init();
          yield ctx.reset();
        }
      } else if (String(res.info.shortPickType) === '1') { // 未完成
        message.success(`${t('短拣成功！你还需重新点数')}${res.info.waitOffNum}${t('件')}`);
        // shortPickType：1则是新增的点数短拣，2则是原有的全部短拣
        yield put((draft) => {
          draft.locationDisabled = 1;
          draft.barCodeDisabled = 1;
          draft.status = action.status;
          draft.info = {
            ...action.info, // 只修改待拣数量
            hadOffNum: res.info.taskGoodsInfo.hadOffNum,
            waitOffNum: res.info.waitOffNum,
            itemHadOffNum: res.info.itemHadOffNum,
            itemWaitOffNum: res.info.itemWaitOffNum,
          };
          draft.waitOffNumInput = res.info.waitOffNum || action.info.waitOffNum;
        });
        classFocus(action.locationInput ? 'barCode' : 'location');
      } else { // 未完成-2全部短拣【按原交互逻辑】
        message.success(t('短拣成功！'));
        yield put((draft) => {
          draft.locationDisabled = 1;
          draft.locationInput = '';
          draft.barCodeDisabled = 1;
          draft.goodsSnPrint = '';
          draft.status = action.status;
          draft.info = { ...action.info, ...res.info.taskGoodsInfo };
          draft.waitOffNumInput = res.info.taskGoodsInfo.waitOffNum || action.info.waitOffNum;
        });
        // console.log(action.status);
        classFocus('location');
      }
    }
  },
  // 整托下架
  * allDown(action, ctx, put) {
    const res = yield downPallet({});
    if (res.code !== '0') {
      modal.error({
        content: res.msg,
      });
    } else {
      yield put((draft) => {
        draft.status = 'allDownPage';
        draft.info = res.info;
        draft.orderType = res.info.orderType;
        draft.orderTypeCode = res.info.orderTypeCode || '';
      });
    }
  },
  // 扫描托盘号
  * scanPallet(action, ctx, put) {
    yield put((draft) => {
      draft.palletDisabled = 0;
    });
    const res = yield scanPallet(action.params);
    if (res.code !== '0') {
      yield put((draft) => {
        draft.palletCode = '';
        draft.palletDisabled = 1;
      });
      modal.error({
        content: res.msg,
        onOk: () => {
          classFocus('pallet');
        },
      });
    } else {
      yield put((draft) => {
        draft.boxNum = res.info.boxNum;
        draft.list = res.info.list;
        draft.boxNumDisabled = 1;
      });
      classFocus('boxNum');
    }
  },
  // 整托下架
  * doDownPallet(action, ctx, put) {
    const res = yield doDownPallet(action.params);
    if (res.code !== '0') {
      yield put((draft) => {
        draft.nums = '';
        draft.boxNumDisabled = 1;
        draft.containerDisabled = false;
        draft.containerCode = '';
      });
      modal.error({
        content: res.msg,
        onOk: () => {
          classFocus('boxNum');
        },
      });
    } else {
      if (res.info.type === 2) {
        yield ctx.init();
      } else {
        yield ctx.init();
        yield put((draft) => {
          draft.status = 'allDownPage';
          draft.orderType = action.params.orderType;
          draft.containerCode = '';
          draft.info = { ...res.info.taskGoodsInfo };
          draft.waitOffNumInput = res.info.taskGoodsInfo.waitOffNum || action.info.waitOffNum;
        });
        classFocus('location');
      }
    }
  },
  // 整托下架 扫描拣货周转箱
  * reScanContainer(action, ctx, put) {
    yield put((draft) => {
      draft.containerDisabled = true;
    });
    if (action.params.containerCode === action.palletCode) {
      yield ctx.doDownPallet({
        params: {
          list: action.list,
          location: action.location,
          palletCode: action.palletCode,
          orderType: action.orderType,
          replenishmentCode: action.replenishmentCode,
          shiftOrderCode: action.shiftOrderCode,
        },
      });
    } else {
      const i = action.containerListBack.indexOf(action.params.containerCode);
      if (i !== -1) {
        const arr = action.containerListBack.slice();
        arr.splice(i, 1);
        yield put((draft) => {
          draft.containerListBack = arr;
          draft.scanContainerList = [...action.scanContainerList, action.params.containerCode];
          draft.nums = (+action.nums || 0) + 1;
        });
        if (arr.length === 0) {
          yield ctx.doDownPallet({
            params: {
              list: action.list,
              location: action.location,
              palletCode: action.palletCode,
              orderType: action.orderType,
              replenishmentCode: action.replenishmentCode,
              shiftOrderCode: action.shiftOrderCode,
            },
          });
        } else {
          yield put((draft) => {
            draft.containerDisabled = false;
            draft.containerCode = '';
          });
          classFocus('box');
        }
      } else {
        yield put((draft) => {
          draft.containerDisabled = false;
          draft.containerCode = '';
        });
        modal.error({
          content: action.scanContainerList.includes(action.params.containerCode)
            ? t('该周转箱已扫描过')
            : t('无法匹配到数据'),
          onOk: () => {
            classFocus('box');
          },
        });
      }
    }
  },
  // 点击明细
  * getDetail(action, ctx, put) {
    markStatus('dataLoading');
    const res = yield getDetailApi(action.params);
    if (res.code === '0') {
      yield put((draft) => {
        draft.showDetail = true;
        draft.detailList = [
          res.info.alreadyUnderGoodsInfo || [], res.info.waitUnderGoodsInfo || [],
        ];
      });
    } else {
      modal.error({ content: res.msg });
    }
  },
  // 整货下架 扫描周转箱
  * scanContainerByBox(action, ctx, put) {
    markStatus('dataLoading');
    const { code, info, msg } = yield scanPickContainerByBoxAPI(action.params);
    if (code === '0') {
      if (info.taskInfo && info.taskInfo.containerCode === null) {
        yield ctx.changeData({ data: { containerCodeInputByBox: '' } });
        yield ctx.reset();
        yield new Promise((r) => modal.error({
          content: t('整箱下架:库位找不到对应的箱子'),
          okText: t('确定'),
          onOk: () => r(1),
        }));
        return;
      }

      if (info.status === 1) {
        // 1 下架未完成
        // 明细更新
        yield put((draft) => {
          draft.info = { ...action.info, ...info.taskInfo };
          draft.containerCodeInputByBox = '';
        });
        classFocus('boxByBox');
      } else if (info.status === 2) {
        // 2 下架完成
        const msgContent = (
          <>
            {t('任务已结束')}
            {
              ![null, ''].includes(info.priority) &&
              (<span>,{t('任务优先级')}-<span style={{ color: 'red' }}>{info.priority}</span></span>)
            }
            {
              info.recommendUpperParkName &&
              (<span>,{t('推荐上架园区')}: {info.recommendUpperParkName}</span>)
            }
            {
              Number(info.recommendedTasksRsp?.isAcrossArea) === 1 &&
              (<span>{`, ${t('任务将由上一个任务的{}库区转移到{}库区', info.recommendedTasksRsp.currentArea, info.recommendedTasksRsp.area)}`}</span>)
            }
          </>
        );

        yield new Promise((r) => modal.success({
          content: msgContent,
          okText: t('确定'),
          onOk: () => r(1),
        }));
        yield ctx.changeData({ data: { containerCodeInputByBox: '' } });
        yield ctx.reset();
      }
    } else if (code === '503801') {
      // 重新领取新的补货任务
      const status = yield new Promise((r) => modal.error({
        content: msg,
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
        yield ctx.reset();
      }
    } else {
      modal.error({
        content: msg,
      });
    }
  },
  // 整货下架 短拣
  * shortPickByBox(action, ctx, put) {
    markStatus('dataLoading');
    const { code, info, msg } = yield shortPickByBoxAPI(action.params);
    if (code === '0') {
      if (info.taskInfo && info.taskInfo.containerCode === null) {
        yield ctx.changeData({ data: { containerCodeInputByBox: '' } });
        yield ctx.reset();
        yield new Promise((r) => modal.error({
          content: t('整箱短拣:库位找不到对应的箱子'),
          okText: t('确定'),
          onOk: () => r(1),
        }));
        return;
      }
      if (info.type === 1) {
        // 1 下架未完成
        // 明细更新
        yield put((draft) => {
          draft.info = { ...action.info, ...info.taskInfo };
          draft.containerCodeInputByBox = '';
        });
        classFocus('boxByBox');
      } else if (info.type === 2) {
        // 2 短拣完成
        const msgContent = (
          <>
            {t('短拣成功，请下架空周转箱：{}', action.params.containerCode)}
            {
              ![null, ''].includes(info.priority) &&
              (<span>,{t('任务优先级')}-<span style={{ color: 'red' }}>{info.priority}</span></span>)
            }
            {
              Number(info.recommendedTasksRsp?.isAcrossArea) === 1 &&
              (<span>{`, ${t('任务将由上一个任务的{}库区转移到{}库区', info.recommendedTasksRsp.currentArea, info.recommendedTasksRsp.area)}`}</span>)
            }
          </>
        );

        yield new Promise((r) => modal.success({
          content: msgContent,
          okText: t('确定'),
          onOk: () => r(1),
        }));
        yield ctx.changeData({ data: { containerCodeInputByBox: '' } });
        yield ctx.reset();
      }
    } else if (code === '503801') {
      // 重新领取新的补货任务
      const status = yield new Promise((r) => modal.error({
        content: msg,
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
        yield ctx.reset();
      }
    } else {
      modal.error({
        content: msg,
      });
    }
  },

  // 获取历史装箱信息
  * getHistory(action, ctx, put) {
    const { code, info, msg } = yield getHistoryApi({ shiftOrderType: 1 });
    if (code === '0') {
      if (info && info.length) {
        yield put((draft) => {
          draft.historyList = info || [];
          draft.showHistoryPage = true;
        });
      } else {
        Modal.info({
          content: t('我的上架历史数据为空'),
        });
      }
    } else {
      Modal.error({ title: msg });
    }
  },

  // 炬星整箱下架 任务领取
  * scanAutoMovingTaskNo(action, ctx, put) {
    markStatus('dataLoading');
    const { code, info, msg } = yield obtainUnderTaskAutoMovingAPI(action.params);
    if (code === '0') {
      yield put((draft) => {
        draft.status = 'containerPage';
        draft.containerCodeInputByBox = '';
        draft.info = info;
      });
      classFocus('boxByBox');
    } else {
      modal.error({
        content: msg,
      });
    }
  },

  // 海柔自动化空箱处理
  * autoEmptyBox(action, ctx) {
    markStatus('dataLoading');
    const { code, msg, info } = yield autoEmptyBoxAPI(action.params);
    if (code === '0') {
      const emptyContent = (
        <>
          <div>{t('由于箱空，移位单已结束')}</div>
          <div>{t('请点击关箱更换新周转箱')}</div>
        </>
      );
      const status = yield new Promise((r) => modal.success({
        content: info?.isEmpty ? emptyContent : t('任务已结束，点击确定返回任务领取页面'),
        okText: t('确定'),
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.reset();
      }
    } else {
      modal.error({
        content: msg,
      });
    }
  },

  // 自动化库区下架 领取任务
  * obtainAutoUnderTask() {
    markStatus('dataLoading');
    const { code, info, msg } = yield obtainAutoUnderTaskAPI();
    // info = null;
    if (code === '0') {
      if (info?.replenishmentCode) {
        yield this.changeData({
          data: {
            info: info || {},
            status: 'normalPage',
            orderType: info.orderType,
            orderTypeCode: info.orderTypeCode || '',
            containerCode: info.containerCode || '',
            shortPickType: info.type,
            waitOffNumInput: info.waitOffNum || '',
            containerDisabled: !!info.containerCode,
            locationInput: info.location || '',
            locationDisabled: !!info.location,
          },
        });
      } else {
        yield this.changeData({
          data: {
            info: info || {},
            status: 'autoTaskPage',
            autoPickContainer: '',
            autoPickContainerDisabled: false,
            autoLocationContainer: '',
            autoLocationContainerDisabled: true,
          },
        });
        classFocus('autoPickContainer');
      }
    } else {
      yield this.changeData({
        data: {
          info: {},
          status: 'initPage',
        },
      });
      yield new Promise((r) => modal.error({
        content: msg,
        onOk: () => r(1),
      }));
    }
  },

  // 自动化库区下架 扫描拣货容器号
  * scanAutoPickContainer(action) {
    markStatus('dataLoading');
    const { code, info, msg } = yield autoScanPickContainerAPI(action.params);
    if (code === '0') {
      yield this.changeData({
        data: {
          autoPickContainer: info,
          autoPickContainerDisabled: true,
          autoLocationContainerDisabled: false,
        },
      });
      classFocus('autoLocationContainer');
    } else {
      yield this.changeData({
        data: {
          autoPickContainer: '', // 清空周转箱
        },
      });
      modal.error({ content: msg, className: 'autoPickContainer' });
    }
  },

  // 自动化库区下架 扫描货位周转箱号
  * scanAutoLocationContainer(action) {
    const { autoPickContainer } = yield '';
    markStatus('dataLoading');
    const { code, info, msg } = yield autoScanLocationContainerAPI(action.params);
    if (code === '0') {
      yield this.changeData({
        data: {
          info: {
            ...(info || {}),
            containerCode: autoPickContainer || '',
          },
          status: 'normalPage',
          orderType: info.orderType,
          orderTypeCode: info.orderTypeCode || '',
          containerCode: autoPickContainer || '',
          shortPickType: info.type,
          waitOffNumInput: info.waitOffNum || '',
          containerDisabled: !!autoPickContainer,
          locationInput: info.location || '',
          locationDisabled: !!info.location,
        },
      });
      // classFocus
      let classFocusClassName = 'barCode';
      if (!info.location) {
        classFocusClassName = 'location';
      }
      if (!info.containerCode) {
        classFocusClassName = 'containerCode';
      }
      classFocus(classFocusClassName);
    } else if (['500608', '503701', '503702'].includes(code)) {
      // 500608 你没有绑定自动化库区 红色
      // 503701 整箱预占下架任务，请直接取出周转箱 绿色
      // 503702 当前周转箱找不到已取箱的自动化任务，请换一个重试 红色
      // 清空聚焦输入框
      yield this.changeData({
        data: {
          autoLocationContainer: '',
        },
      });
      modal.error({
        content: code === '503701' ? <span style={{ color: '#65cc2f' }}>{msg}</span> : <span style={{ color: 'red' }}>{msg}</span>,
        className: 'autoLocationContainer',
      });
    } else {
      // 清空聚焦输入框
      yield this.changeData({
        data: {
          autoLocationContainer: '', // 清空货位周转箱号
        },
      });
      modal.error({ content: msg, className: 'autoLocationContainer' });
    }
  },

};
