import React from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import store from './reducers';
import { Form } from 'react-weui/build/packages/components/form';
import RowInfo from '../../common/row-info';

const BaseData = (props) => {
  const {
    data,
  } = props;
  return (
    <div>
      <Form style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        {
          data.map(item => (
            <RowInfo
              key={item.label}
              extraStyle={{
                borderBottom: 'none',
              }}
              label={item.label}
              content={<b style={{ paddingLeft: '15px', color: 'red' }}>{item.num}单</b>}
              type={item.type}
            />
          ))
        }
        <div
          onClick={() => {
            store.getInfo();
         }}
          style={{ paddingRight: '15px', color: '#0059ce' }}
        >
          <Icon name="sync" />
        </div>
      </Form>
    </div>
  );
};

BaseData.propTypes = {
};

export default BaseData;
