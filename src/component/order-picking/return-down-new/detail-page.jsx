import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import { NavDetail, pages, Footer } from '../../common';
import store from './reducers';

const { View } = pages;

const rows1 = [
  [
    {
      title: t('SKC'),
      render: 'goodsSn',
    },
    {
      title: t('数量'),
      render: 'count',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('尺码/属性集'),
      // render: 'size',
      render: row => (
        <span>{row.saleAttrZh ? row.saleAttrZh : row.size}</span>
      ),
    },
    {
      title: t('库位'),
      render: row => (
        <div>
          <span style={{ color: '#666c7c', marginRight: 7 }}>{t('库位')}</span>
          <span style={{ color: 'red' }}>
            <span>{row.location}</span>
          </span>
          {
              (row.sequence !== 0 && row.sequence !== null) && (
                <span>--{row.sequence}</span>
              )
            }
        </div>
      ),
    },
  ],
  [
    {
      title: t('周转箱'),
      render: 'containerCode',
    },
  ],
];
const rows2 = [
  [
    {
      title: t('SKC'),
      render: 'goodsSn',
    },
    {
      title: t('数量'),
      render: 'count',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('尺码/属性集'),
      // render: 'size',
      render: row => (
        <span>{row.saleAttrZh ? row.saleAttrZh : row.size}</span>
      ),
    },
    {
      title: t('库位'),
      render: row => (
        <div>
          <span style={{ color: '#666c7c', marginRight: 7 }}>{t('库位')}</span>
          <span style={{ color: 'red' }}>
            <span>{row.location}</span>
          </span>
          {
              (row.sequence !== 0 && row.sequence !== null) && (
                <span>--{row.sequence}</span>
              )
            }
        </div>
      ),
    },
  ],
];

const DetailPage = (props) => {
  const {
    data,
    dispatch,
  } = props;

  const rowsList = [
    rows1, rows2,
  ];
  const hasPickNum = data[0].reduce((pre, current) => pre + current.count, 0);
  const noPickkNum = data[1].reduce((pre, current) => pre + current.count, 0);
  const totalNumList = [hasPickNum, noPickkNum];
  const navList = [t('已拣选'), t('未拣选')];
  return (
    <View diff={100}>
      <NavDetail
        data={data}
        rowsList={rowsList}
        navList={navList}
        totalNumList={totalNumList}
        imgUrlFieldName="imageUrl"
      />
      <Footer
        dispatch={dispatch}
        beforeBack={() => {
          store.changeData({
            data: {
              showDetail: false,
            },
          });
        }}
      />
    </View>
  );
};

DetailPage.propTypes = {
  data: PropTypes.arrayOf(PropTypes.array),
  dispatch: PropTypes.func,
};

export default DetailPage;
