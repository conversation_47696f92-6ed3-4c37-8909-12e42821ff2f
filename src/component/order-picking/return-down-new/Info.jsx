import React from 'react';
import { t } from '@shein-bbl/react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages/components/form';
import style from './style.css';

const Info = (props) => {
  const {
    data,
    orderType,
  } = props;
  const location = data.location || '';
  return (
    <div>
      <Form>
        <div className={style.firstItem}>
          <span>{data.replenishmentCode}</span>
          <div>
            {t('已拣/总件数')}: 
            <span className={style.weight}>
              <span style={{ color: '#ff9636' }}>{data.hadOffNum}</span>
              /{data.goodsTotalNum}
            </span>
          </div>
        </div>
      </Form>
      <Form style={{
        paddingTop: 2,
      }}
      >
        <div className={style.mainInfo}>
          <div className={style.mainItem}>
            <span>{t('库位号')}</span>
            <div style={{ flex: 1, textAlign: 'right' }}>
              <span style={{ fontSize: 16, fontWeight: 'normal' }}>
                {location.substring(0, 6)}
                <b style={{ fontSize: 20 }}>{location.substring(6, 14)}</b>
                <span style={{ color: '#f00' }}>{location.substring(14)}</span>
              </span>
              {
                orderType ? (
                  <button
                    style={{
                      width: '60px',
                      marginLeft: '10px',
                    }}
                    className={style.btn}
                  >{orderType}
                  </button>
                ) : null
              }
            </div>
          </div>
          <div className={style.mainItem}>
            <span>{t('SKC')}</span>
            <div style={{ color: '#FF0000' }}>
              {data.goodsSn}
            </div>
          </div>
          <div className={classnames(style.mainItem, style.sizeAndColorItem)}>
            <div
              style={{
                fontSize: 25,
                fontWeight: 'bold',
                color: '#FF0000',
                marginRight: 50,
              }}
            >
              {data.color}
            </div>
            <div data-if={data.saleAttrZh} style={{ fontSize: 14, fontWeight: 'bold', color: '#FF0000' }}>
              {data.saleAttrZh}
            </div>
            <div data-if={!data.saleAttrZh} style={{ fontSize: 25, fontWeight: 'bold', color: '#FF0000' }}>
              {data.size}
            </div>
          </div>
          <div className={style.mainItem}>
            <span>{t('退供子仓')}</span>
            <div style={{ color: '#FF0000' }}>
              {data.returnSubWarehouse}
            </div>
          </div>
        </div>
      </Form>
    </div>
  );
};

Info.propTypes = {
  data: PropTypes.object,
};

export default Info;
