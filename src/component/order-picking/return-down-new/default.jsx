import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import Footer from '../../common/footer';
import { Form } from 'react-weui/build/packages/components/form';
import LoadMore from 'react-weui/build/packages/components/loadmore';
import FocusInput from '../../common/focus-input';
import BaseData from './base-data';
import store from './reducers';
import {push} from "react-router-redux";

const DefaultPage = (props) => {
  const {
    dataLoading,
    baseData,
    boxDisabled,
    dispatch,
  } = props;
  return (
    <div>
      {
        dataLoading === 0 ? <LoadMore loading={dataLoading === 0} /> : <BaseData data={baseData} />
      }
      <Form
        style={{marginTop: '10px'}}
      >
        <FocusInput
          placeholder={t('请扫描')}
          data-bind="containerCode"
          onPressEnter={(e) => {
            if (e.target.value) {
              store.scanBox(e.target.value.trim());
            }
          }}
          disabled={boxDisabled === 0}
          className="box"
        >
          <label>{t('拣货周转箱')}</label>
        </FocusInput>
      </Form>
      <Footer beforeBack={(back) => {
        store.init();
        back();
      }} />
    </div>
  );
};

DefaultPage.propTypes = {
  dataLoading: PropTypes.number,
};

export default DefaultPage;
