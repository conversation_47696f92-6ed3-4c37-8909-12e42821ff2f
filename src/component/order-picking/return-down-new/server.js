import { sendPostRequest } from '../../../lib/public-request';

export const queryPurchaseReturnInfo = (param) => sendPostRequest({
  url: '/replenish_shelves/query_purchase_return_task_info',
  param,
}, process.env.WIS_FRONT);
/**
 * 扫描库位
 * @param param
 * @returns {*}
 */
export const scanLocation = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_location',
  param,
}, process.env.WIS_FRONT);
/**
 * 扫描托盘号
 * @param param
 * @returns {*}
 */
export const scanPallet = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_pallet',
  param,
}, process.env.WIS_FRONT);
/**
 * 短拣
 * @param param
 * @returns {*}
 */
export const shortPick = (param) => sendPostRequest({
  url: '/replenish_shelves/short_pick',
  param,
}, process.env.WWS_URI);

export const scanBox = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_purchase_return_pick_container',
  param,
}, process.env.WIS_FRONT);
export const scanSku = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_purchase_return_barcode',
  param,
}, process.env.WIS_FRONT);

export const submitGoodsCount = (param) => sendPostRequest({
  url: '/replenish_shelves/submit_purchase_return_goods_count',
  param,
}, process.env.WIS_FRONT);
export const closeBox = (param) => sendPostRequest({
  url: '/replenish_shelves/close_purchase_return_pick_container',
  param,
}, process.env.WIS_FRONT);

export const pdaShortPick = (param) => sendPostRequest({
  url: '/replenish_shelves/purchase_return_short_pick',
  param,
}, process.env.WIS_FRONT);

export const getDetailApi = (param) => sendPostRequest({
  url: '/replenish_shelves/query_record_detail',
  param,
}, process.env.WIS_FRONT);
