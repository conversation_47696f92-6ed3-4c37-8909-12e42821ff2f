import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { select } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import { paramTrim } from 'lib/format';
import Modal from 'common/modal';
import { modal, message } from '../../common';

import {
  pageInitApi,
  scanLocationApi,
  scanGoodsApi,
  commitDatasApi,
  closeContainerApi,
  searchDetailApi,
  scanContainerApi,
  getRecommendLocationlApi,
  getHistoryApi,
} from './server';
import { classFocus, getHeaderTitle, compareSku } from '../../../lib/util';

// 货位类型
export const locationTypeMap = new Map([
  [t('大货'), '1'],
  [t('散货'), '2'],
]);

const defaultState = {
  loading: false,
  locationsInfo: [],
  containerCode: '',
  shiftOrderCode: '',
  initContainerCode: '',
  downNum: 0,
  // 下架库位
  locationCode: '',
  // 商品条码
  barCode: '',
  // 商品数量
  num: '',
  // 散货 序列号
  sequence: '',
  // 货位类型：1大货 |2散货
  type: locationTypeMap.get(t('大货')),
  // 扫描商品条码返回的信息
  barCodeRes: [],
  // 序列号输入框是否可用
  // 表格组件
  tableRef: null,
  detailBoxCode: '',
  detailTotalNum: '',
  detailShiftOrderCode: '',
  detailList: [],
  isContainerCodeDisabled: false,
  isLocationDisabled: false,
  isNumDisabled: true,
  isBarCodeDisabled: true,
  isSequenceDisabled: true,
  headerTitle: '',
  // 推荐库位
  recommendLocation: '',
  historyList: [], // 历史数据
  showHistoryPage: false, // 展示历史页面

  // 生产日期
  productionDateShow: false,
  validityList: [], // 生产日期列表
  selectedProductionDate: '', // 选中的生产日期
};

function getTopList(list, infoList) {
  if (!infoList.length) {
    return [];
  }
  let res = [];

  const firstItem = infoList[0];

  const isExist = list.findIndex((i) => compareSku(i, firstItem)) > -1;

  if (isExist) {
    list.forEach((item) => {
      if (compareSku(item, firstItem)) {
        res.unshift(item);
      } else {
        res.push(item);
      }
    });
  } else {
    res = [...infoList, ...list].slice(0, 10);
  }

  return res;
}

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    const res = yield pageInitApi(action.params);
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          initContainerCode: res.info.containerCode,
          downNum: res.info.downNum,
        },
      });
    } else {
      modal.error({ content: res.msg });
    }
  },
  // 清空历史页面状态
  * resetHistory(action, ctx) {
    yield ctx.changeData({ data: { showHistoryPage: false } });
  },
  // 扫描下架库位
  * scanLocation(action, ctx) {
    yield ctx.changeData({
      data: {
        locationsInfo: [],
        barCodeRes: [],
      },
    });
    const data = yield scanLocationApi(paramTrim(action.params));
    yield ctx.changeData({
      data: {
        isLocationDisabled: false,
      },
    });
    if (data.code === '0') {
      const { locationsInfo, type } = data.info;
      yield ctx.changeData({
        data: {
          locationsInfo,
          type: type.toString(),
          isBarCodeDisabled: !locationsInfo.length,
        },
      });
      if (locationsInfo.length > 0) {
        classFocus('barCode');
      } else {
        yield ctx.changeData({ data: { locationCode: '' } });
        modal.error({
          content: t('该库位没有商品'),
          className: 'location',
        });
      }
      // 获取推荐库位
      // yield ctx.getRecommendLocation({
      //   params: {
      //     recommendType: 1,
      //     location: action.params.locationCode,
      //   },
      // });
    } else {
      yield ctx.changeData({ data: { locationCode: '' } });
      modal.error({
        content: data.msg,
        className: 'location',
      });
    }
  },
  // 扫描商品条码
  * scanGoods(action, ctx) {
    const data = yield scanGoodsApi(action.params);
    yield ctx.changeData({ data: { isBarCodeDisabled: false } });
    if (data.code === '0') {
      const { locationsInfo } = yield select((state) => state['order-picking/shift-down']);
      const { validityTag, validityList } = data.info;

      // validityTag 0 不走效期 1 走效期
      if (validityTag === 1) {
        switch (validityList.length) {
          case 0:
            // 库位上的效期和周转箱已有的效期不同
            yield ctx.changeData({
              data: {
                barCode: '',
                validityList: [],
                selectedProductionDate: '',
              },
            });
            yield new Promise((r) => modal.error({
              content: t('生产日期不同，请换箱'),
              onOk: () => r(1),
              className: 'barCode',
            }));
            return;
          case 1:
            // 箱子已有某个效期
            yield ctx.changeData({
              data: {
                selectedProductionDate: validityList[0].productionDate,
              },
            });
            yield ctx.selectDate();
            break;
          default:
            // 库位上有多个效期
            yield new Promise((r) => modal.info({
              content: t('该库位存在不同的生产日期/到期日期商品'),
              onOk: () => r(1),
              className: 'productionDate',
            }));
            break;
        }
      } else {
        yield ctx.selectDate();
      }

      yield ctx.changeData({
        data: {
          barCodeRes: data.info.locationsInfo,
          locationsInfo: getTopList(locationsInfo, data.info.locationsInfo),
          validityList: validityTag === 1 ? data.info.validityList : [],
        },
      });
    } else if (data.code === '503002') {
      const { shiftOrderCode, containerCode } = yield select((state) => state['order-picking/shift-down']);
      const status = yield new Promise((r) => modal.confirm({
        content: t('当前扫描商品，在周转箱上已经存在，不允许混放，请关箱！'),
        onOk: () => r(1),
        onCancel: () => r(2),
        okText: t('关箱'),
        cancelText: t('返回'),
      }));
      if (status === 1) {
        yield ctx.closeContainer({
          params: {
            containerCode,
            shiftOrderCode,
          },
        });
      }
      // 1. 清空库位,条码
      // 2. 光标定位到库位
      if (status === 2) {
        yield ctx.changeData({
          data: {
            locationCode: '',
            barCode: '',
            isLocationDisabled: false,
            isBarCodeDisabled: true,
          },
        });
        classFocus('location');
      }
    } else if (data.code === '503801') {
      // 跳转到仓内移位、返仓移位下架首页
      const status = yield new Promise((r) => modal.error({
        content: data.msg,
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
      }
    } else {
      yield ctx.changeData({
        data: {
          barCode: '',
          validityList: [],
          selectedProductionDate: '',
        },
      });
      modal.error({
        content: data.msg,
        className: 'barCode',
      });
    }
  },
  // 选择效期
  * selectDate(action, ctx) {
    const { type } = yield select((state) => state['order-picking/shift-down']);

    // if (type === locationTypeMap.get(t('大货'))) {
    //   // 数量
    //   classFocus('num');
    //   yield ctx.changeData({
    //     data: {
    //       isNumDisabled: false,
    //     },
    //   });
    // } else {
    //   // 序列号
    //   classFocus('sequence');
    //   yield ctx.changeData({
    //     data: {
    //       isSequenceDisabled: false,
    //     },
    //   });
    // }
    // 大货、散货都去录入数量
    classFocus('num');
    yield ctx.changeData({
      data: {
        isNumDisabled: false,
      },
    });
  },
  // 扫描数量(大货)/扫描序列号(散货)
  * commitDatas(action, ctx) {
    const {
      locationsInfo,
      sequence,
      validityList,
      selectedProductionDate,
      barCodeRes,
    } = yield select((state) => state['order-picking/shift-down']);

    let date = validityList.find((x) => x.productionDate === selectedProductionDate);

    // 非效期数据也传productionDate/expiringDate
    if (!date && barCodeRes.length) {
      const findMobileIndex = barCodeRes.findIndex((x) => x.mobileNum > 0);

      if (findMobileIndex === -1) {
        date = {
          productionDate: barCodeRes[0].productionDate,
          expiringDate: barCodeRes[0].expiringDate,
        };
      } else {
        date = {
          productionDate: barCodeRes[findMobileIndex].productionDate,
          expiringDate: barCodeRes[findMobileIndex].expiringDate,
        };
      }
    }
    const params = {
      ...paramTrim(action.params),
      barCode: action.params.barCode,
      productionDate: date?.productionDate || null,
      expiringDate: date?.expiringDate || null,
    };

    const data = yield commitDatasApi(params);
    if (data.code === '0') {
      if (data.info) {
        yield new Promise((r) => modal.info({
          // content: t('下架成功，请下架空周转箱：{}', data.info),
          content: t('库位已无在库库存，可将空箱下架'),
          onOk: () => r(1),
        }));
      } else {
        message.success(t('下架成功'));
      }

      // 货位类型：1大货 |2散货
      if (action.params.type === locationTypeMap.get(t('大货'))) {
        // 1 大货
        yield ctx.changeData({
          data: {
            locationCode: '',
            barCode: '',
            num: '',
            sequence: '',
            locationsInfo: [],
            type: action.params.type,
            isBarCodeDisabled: true,
            isNumDisabled: true,
            isSequenceDisabled: true,
            validityList: [],
            selectedProductionDate: '',
          },
        });
        classFocus('location');
      } else {
        // 2 散货
        yield ctx.changeData({
          data: {
            barCode: '',
            num: '',
            sequence: '',
            locationsInfo,
            type: action.params.type,
            isBarCodeDisabled: false,
            isNumDisabled: true,
            isSequenceDisabled: true,
            validityList: [],
            selectedProductionDate: '',
          },
        });
        classFocus('barCode');
      }

      // 获取推荐库位
      // yield ctx.getRecommendLocation({
      //   params: {
      //     recommendType: 2,
      //     barCode: action.params.barCode,
      //     location: action.params.location,
      //   },
      // });
    } else {
      yield ctx.changeData({
        data: {
          barCode: '',
          num: '',
          sequence: '',
          validityList: [],
          selectedProductionDate: '',
        },
      });
      modal.error({ content: data.msg, className: 'barCode' });
    }
  },
  // 关箱
  * closeContainer(action, ctx, put) {
    const data = yield closeContainerApi(action.params);
    if (data.code === '0') {
      message.success(t('关箱成功'));
      yield put(push('/order-picking/shift-down'));
    } else if (data.code === '503801') {
      // 页面跳转至仓内移位、返仓移位下架首页
      const status = yield new Promise((r) => modal.error({
        content: data.msg,
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
      }
    } else {
      modal.error({ content: data.msg });
    }
  },
  // 扫描周转箱
  * scanContainer(action, ctx, put) {
    const data = yield scanContainerApi(action.params);
    yield ctx.changeData({
      data: {
        isContainerCodeDisabled: false,
      },
    });
    if (data.code === '0') {
      const { containerCode, shiftOrder } = data.info;
      yield ctx.changeData({
        data: {
          shiftOrderCode: shiftOrder,
          containerCode,
        },
      });
      yield put(push('/order-picking/shift-down/picking-page'));
      setTimeout(() => {
        classFocus('location');
      }, 100);
    } else {
      yield ctx.changeData({
        data: {
          containerCode: '',
        },
      });
      modal.error({ content: data.msg, className: 'containerCode' });
    }
  },
  // 清空下架库位
  * clearLocation(action, ctx) {
    yield ctx.changeData({
      data: {
        locationCode: '',
        barCode: '',
        num: '',
        sequence: '',
        isBarCodeDisabled: true,
        isNumDisabled: true,
        isSequenceDisabled: true,
        locationsInfo: [],
        barCodeRes: [],
        validityList: [],
        selectedProductionDate: '',
      },
    });
    classFocus('location');
  },
  // 明细
  * searchDetail(action, ctx, put) {
    const data = yield searchDetailApi(action.params);
    if (data.code === '0') {
      const {
        containerCode, allGoodsNum, shiftOrderCode, shiftDownDetailGoods,
      } = data.info;
      yield ctx.changeData({
        data: {
          detailBoxCode: containerCode,
          detailTotalNum: allGoodsNum,
          detailShiftOrderCode: shiftOrderCode,
          detailList: shiftDownDetailGoods || [],
        },
      });
      yield put(push('/order-picking/shift-down/detail'));
    } else {
      modal.error({
        content: data.msg,
      });
    }
  },
  // 获取推荐库位
  * getRecommendLocation(action, ctx) {
    const data = yield getRecommendLocationlApi(action.params);
    if (data.code === '0') {
      yield ctx.changeData({ data: { recommendLocation: data.info ? data.info.location : '' } });
    } else {
      yield ctx.changeData({ data: { recommendLocation: '' } });
      console.log(data.msg);
    }
  },
  // 获取历史装箱信息
  * getHistory(action, ctx, put) {
    const { code, info, msg } = yield getHistoryApi({ shiftOrderType: 4 });
    if (code === '0') {
      if (info && info.length) {
        yield put((draft) => {
          draft.historyList = info || [];
          draft.showHistoryPage = true;
        });
      } else {
        Modal.info({
          content: t('我的上架历史数据为空'),
        });
      }
    } else {
      Modal.error({ title: msg });
    }
  },
};
