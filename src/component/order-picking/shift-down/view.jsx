import React, { Component } from 'react';
import { i18n, t } from '@shein-bbl/react';
import { connect } from 'react-redux';
import Icon from '@shein-components/Icon';
import PropTypes from 'prop-types';
import ScanContainer from './jsx/scan-container';
import PickingPage from './jsx/picking-page';
import DetailList from './jsx/detail';
import HistoryPage from './jsx/history';

import { Header } from '../../common';
import store from './reducers';
import navStore from '../../nav/reducers';
import { classFocus } from '../../../lib/util';

class Container extends Component {
  componentDidMount() {
    store.resetHistory();
  }

  render() {
    const {
      containerCode, shiftOrderCode, locationCode, match, headerTitle, showHistoryPage,
    } = this.props;

    const { status } = match.params;

    let children;

    switch (status) {
      case 'detail':
        children = (<DetailList {...this.props} />);
        break;
      case 'picking-page':
        children = (<PickingPage {...this.props} />);
        break;
      default:
        children = (<ScanContainer {...this.props} />);
        break;
    }
    if (showHistoryPage) {
      return <HistoryPage {...this.props} />;
    }
    return (
      <div>
        <Header title={headerTitle || t('移位下架')}>
          {status === 'picking-page' && (
            <div
              onClick={() => {
                store.searchDetail({
                  params: {
                    containerCode,
                    shiftOrderCode,
                    location: locationCode,
                  },
                });
              }}
            >
              <Icon name="detail" style={{ marginRight: '5px' }} />
              {t('明细')}
            </div>
          )}
          {showHistoryPage || (
            <div
              onClick={() => {
                store.getHistory();
              }}
            >
              {t('历史')}
            </div>
          )}
        </Header>
        {children}
        {/*<DragCircle*/}
        {/*  onClick={() => {*/}
        {/*    navStore.changeData({ data: { showUploadError: true } });*/}
        {/*    navStore.changeLimit({ data: { location: locationCode || '' } });*/}
        {/*    classFocus('location');*/}
        {/*  }}*/}
        {/*/>*/}
      </div>
    );
  }
}

Container.propTypes = {
  match: PropTypes.shape(),
  containerCode: PropTypes.string,
  shiftOrderCode: PropTypes.string,
  locationCode: PropTypes.string,
  headerTitle: PropTypes.string,
  showHistoryPage: PropTypes.bool,
};

const mapStateToProps = (state) => state['order-picking/shift-down'];
export default connect(mapStateToProps)(i18n(Container));
