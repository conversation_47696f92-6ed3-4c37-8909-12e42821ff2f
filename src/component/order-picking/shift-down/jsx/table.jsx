import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import List from '../../../common/list';
import innerStyle from '../style.css';
import { locationTypeMap } from '../reducers';

function LocationTable(props) {
  const {
    type,
    locationsInfo,
    barCodeRes,
    validityList,
    selectedProductionDate,
  } = props;

  const rows = [
    [
      {
        title: t('SKC'),
        render: 'skc',
      },
      {
        title: t('尺码'),
        render: 'size',
      },
    ],
  ];

  if (locationsInfo.find((x) => x.productionDate !== null)) {
    rows.push([
      {
        render: (x) => {
          if (x.productionDate) {
            return (
              <span>
                <span className={innerStyle.label}>{t('生产日期')}</span>
                <span className={innerStyle.value}>{x.productionDate.split(' ')[0]}</span>
              </span>
            );
          } else {
            return false;
          }
        },
      },
    ]);
    rows.push([
      {
        render: (x) => {
          if (x.expiringDate) {
            return (
              <span>
                <span className={innerStyle.label}>{t('到期日期')}</span>
                <span className={innerStyle.value}>{x.expiringDate.split(' ')[0]}</span>
              </span>
            );
          } else {
            return false;
          }
        },
      },
    ]);
  }

  // 货位类型：1大货 |2散货
  // 散货不用展示序列号了
  rows.push([{
    title: t('可移'),
    render: 'mobileNum',
    default: 0,
  }, {
    title: t('不可移'),
    render: 'nonMobileNum',
    default: 0,
  }]);
  // if (type === locationTypeMap.get(t('大货'))) {
  //   rows.push([{
  //     title: t('可移'),
  //     render: 'mobileNum',
  //     default: 0,
  //   }, {
  //     title: t('不可移'),
  //     render: 'nonMobileNum',
  //     default: 0,
  //   }]);
  // } else {
  //   rows.push([{
  //     title: t('序列号'),
  //     render: 'sequence',
  //     default: 0,
  //   }]);
  // }

  const findMobileIndex = locationsInfo.findIndex((x) => x.mobileNum > 0);
  const currentGoodSkuCode = locationsInfo[0]?.skuCode || '';

  return (
    <List
      rowStyleOrClass={(item, index) => {
        // 存在效期，标红对应效期
        if (validityList.length > 0) {
          if (item.productionDate === selectedProductionDate &&
            item.skuCode === currentGoodSkuCode) {
            return innerStyle.scan_success;
          } else {
            return '';
          }
        }
        // 默认标红第一条可移数据，否则标红第一条
        if (barCodeRes.length) {
          if (findMobileIndex === -1) {
            if (index === 0) {
              return innerStyle.scan_success;
            }
          } else {
            if (index === findMobileIndex) {
              return innerStyle.scan_success;
            }
          }
        }
        return '';
      }}
      rows={rows}
      data={locationsInfo}
    />

  );
}

LocationTable.propTypes = {
  type: PropTypes.string,
  locationsInfo: PropTypes.arrayOf(PropTypes.shape()),
  barCodeRes: PropTypes.arrayOf(PropTypes.shape()),
  validityList: PropTypes.arrayOf(PropTypes.shape()),
  selectedProductionDate: PropTypes.string,
};

export default LocationTable;
