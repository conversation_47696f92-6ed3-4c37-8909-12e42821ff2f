import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import Footer from '../../../common/footer';
import List from '../../../common/list';
import style from '../../../style.css';
import innerStyle from '../style.css';

const getRows = (data) => {
  const rows = [
    [
      {
        title: t('SKC'),
        render: 'skc',
      },
    ],
    [
      {
        title: t('尺码'),
        render: 'size',
      },
      {
        title: t('下架数量'),
        render: 'num',
        default: 0,
      },
    ],
  ];

  if (Object.prototype.hasOwnProperty.call(data[0] || {}, 'productionDate')) {
    rows.splice(
      1,
      0,
      [{
        title: t('生产日期'),
        render: (rowData) => (rowData.productionDate || '').split(' ')[0],
      }],
      [{
        title: t('到期日期'),
        render: (rowData) => (rowData.expiringDate || '').split(' ')[0],
      }],
    );
  }

  return rows;
};

class DetailList extends Component {
  render() {
    const {
      dispatch,
      detailShiftOrderCode,
      detailList,
      detailBoxCode,
      detailTotalNum,
    } = this.props;

    const height = window.innerHeight - 44 - 56;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <div className={innerStyle.box_mes}>
          <div>
            {t('移位单号')}&nbsp;:&nbsp;
            <span className={style.c_red}>{detailShiftOrderCode}</span>
          </div>
          <div>{t('当前周转箱')}&nbsp;:&nbsp;{detailBoxCode}</div>
          <div>{t('已下架总数')}&nbsp;:&nbsp;{detailTotalNum}</div>
        </div>
        <List
          rows={getRows(detailList)}
          data={detailList}
        />
        <Footer
          beforeBack={() => {
            dispatch(push('/order-picking/move-warehouse-shift-down/picking-page'));
          }}
        />
      </div>
    );
  }
}

DetailList.propTypes = {
  dispatch: PropTypes.func,
  detailShiftOrderCode: PropTypes.string,
  detailList: PropTypes.arrayOf(PropTypes.shape()),
  detailBoxCode: PropTypes.string,
  detailTotalNum: PropTypes.string,
};

export default DetailList;
