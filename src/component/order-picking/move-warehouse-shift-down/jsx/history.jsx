import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import { Header, List, Footer } from 'common';
import store from '../reducers';

const rows = [
  [
    {
      title: 'SKC',
      render: 'skc',
    },
  ],
  [
    {
      title: `${t('库位')}:`,
      render: 'location',
    },
    {
      title: `${t('尺码')}:`,
      render: 'size',
    },
  ],
  [
    {
      render: 'color',
    },
    {
      title: `${t('件数')}:`,
      render: 'downNum',
      default: 0,
    },
  ],
  [
    {
      title: `${t('时间')}:`,
      render: 'underFinishTime',
    },
  ],
];
function HistoryPage(props) {
  const { historyList } = props;
  return (
    <div>
      <Header title={t('我的下架历史')} />
      <List
        rows={rows}
        data={historyList}
        key={(d) => JSON.stringify(d)}
        style={{ height: 'calc(100vh - 110px)', overflowY: 'auto' }}
        rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0', marginLeft: -7 }}
      />
      <Footer
        beforeBack={() => {
          store.changeData({ data: { showHistoryPage: false, historyList: [] } });
        }}
      />
    </div>
  );
}

HistoryPage.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  historyList: PropTypes.arrayOf(PropTypes.object),
};
export default HistoryPage;
