import { sendPostRequest } from '../../../lib/public-request';

/**
//  * 移位下架-页面初始化
//  * @param param
//  * @returns {*}
//  */
// export const pageInitApi = (param) => sendPostRequest({
//   url: '/shift_down/index',
//   param,
// }, process.env.WWS_URI);

// 新页面调用-页面初始化
export const movePageInitApi = (param) => sendPostRequest({
  url: '/shift_down/index_move_warehouse',
  param,
}, process.env.WWS_URI);

/**
 * 移位下架-扫描周转箱
 * @param param
 * @returns {*}
 */
export const scanContainerApi = (param) => sendPostRequest({
  url: '/shift_down/scan_container',
  param,
}, process.env.WWS_URI);

/**
 * 移位下架-扫描仓内库位
 * @param param
 * @returns {*}
 */
export const scanLocationApi = (param) => sendPostRequest({
  url: '/shift_down/scan_location',
  param,
}, process.env.WWS_URI);

/**
 * 移位下架-扫描商品条码
 * @param param
 * @returns {*}
 */
export const scanGoodsApi = (param) => sendPostRequest({
  url: '/shift_down/scan_goods',
  param,
}, process.env.WWS_URI);

/**
 * 移位下架-下架数据提交
 * @param param
 * @returns {*}
 */
export const commitDatasApi = (param) => sendPostRequest({
  url: '/shift_down/commit',
  param,
}, process.env.WWS_URI);

/**
 * 移位下架-关箱
 * @param param
 * @returns {*}
 */
export const closeContainerApi = (param) => sendPostRequest({
  url: '/shift_down/close_container',
  param,
}, process.env.WWS_URI);

/**
 * 移位下架-箱明细
 * @param param
 * @returns {*}
 */
export const searchDetailApi = (param) => sendPostRequest({
  url: '/shift_down/detail/list',
  param,
}, process.env.WWS_URI);

/**
 * 移位下架-获取推荐库位
 * @param param
 * @returns {*}
 */
export const getRecommendLocationlApi = (param) => sendPostRequest({
  url: '/shift/down/recommend/location',
  param,
});

/**
 * 移位下架 - 历史查询
 * @param param
 * @returns {*}
 */
export const getHistoryApi = (param) => sendPostRequest({
  url: '/scan_down/history_down_list',
  param,
}, process.env.WWS_URI);
