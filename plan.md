# 前端代码风险评估和适配计划

## 项目背景
后端接口字段类型从 long 类型变更为 string 类型，需要对前端代码进行风险评估和必要的适配。

## 执行原则
- 分批次、分步骤执行，确保每个步骤都有明确的验收标准
- 优先进行风险评估，再决定是否需要代码修改
- 如果只是 TypeScript 类型定义调整，不进行实际代码改动
- 如果需要代码逻辑改动，必须详细记录改动点和测试场景

## 第一阶段：接口匹配验证

### 任务 1.1：环境变量配置确认
**目标**：确认 config/.legorc.ts 中的环境变量配置，理解完整的接口地址构成

**执行步骤**：
1. 分析 config/.legorc.ts 中的环境变量定义
2. 确认以下关键环境变量的值：
   - `BASE_URI_WMD`: `/wmd/front`
   - `WKB_FRONT`: `/wkb/front` 
   - `STAT_URI`: `/wms/stat/front`
   - `WIS_FRONT`: `/wis/front`

**验收标准**：
- [ ] 完成环境变量映射表
- [ ] 确认每个接口的完整URL构成规则

### 任务 1.2：接口URL匹配验证
**目标**：验证 todo.md 中列出的接口URL与实际代码中的接口定义是否匹配

**执行步骤**：
1. 逐一检查 todo.md 中的每个接口URL
2. 在代码中搜索对应的接口定义
3. 验证接口路径的一致性
4. 记录不匹配的情况

**验收标准**：
- [ ] 完成接口匹配验证报告
- [ ] 标记所有不匹配的接口
- [ ] 确认接口环境变量前缀的正确性

## 第二阶段：接口使用情况分析

### 任务 2.1：接口函数定位
**目标**：确认每个接口函数在代码库中的定义位置和调用方式

**执行步骤**：
1. 分析以下关键接口文件：
   - `src/lib/wkb-front.js` - WKB相关接口
   - `src/lib/wis-front.js` - WIS相关接口
   - `src/component/rfid-manage/rfid-query/server.js` - RFID查询接口
   - `src/component/inbound-manage/location-info-manage/server.js` - 库位管理接口
2. 记录每个接口的函数名称和参数结构
3. 确认接口的调用方式（GET/POST等）

**验收标准**：
- [ ] 完成接口函数清单
- [ ] 记录每个接口的参数结构
- [ ] 确认接口调用方式

### 任务 2.2：组件使用情况分析
**目标**：识别所有调用这些接口的组件和模块

**执行步骤**：
1. 搜索每个接口函数的调用位置
2. 分析调用这些接口的组件：
   - RFID管理相关组件
   - 库位信息管理组件
   - 容器查询相关组件
   - 统计看板相关组件
3. 记录组件的业务功能和使用场景

**验收标准**：
- [ ] 完成组件使用情况清单
- [ ] 记录每个组件的业务场景
- [ ] 标记高频使用的接口

## 第三阶段：字段风险点检查

### 任务 3.1：受影响字段识别
**目标**：识别从 long 转为 string 的字段及其在前端的使用方式

**受影响字段清单**：
- `id` - 各种实体的主键ID
- `extendId` - 扩展ID，用于库位规格等
- `targetId` - 目标ID，用于统计相关功能
- `supplierId` - 供应商ID
- `pointId` - 点位ID
- `jobTypeId` - 作业类型ID
- `jobId` - 作业ID
- `packageId` - 包裹ID
- `taskId` - 任务ID

**执行步骤**：
1. 分析每个字段在前端代码中的使用方式
2. 检查是否存在数值计算、比较操作
3. 检查是否用于数组索引或对象键值
4. 检查是否存在类型转换操作

**验收标准**：
- [ ] 完成字段使用方式分析报告
- [ ] 识别所有潜在风险点
- [ ] 分类风险等级（高/中/低）

### 任务 3.2：数值操作风险评估
**目标**：重点检查涉及数值计算和比较的代码段

**重点检查场景**：
1. 数值比较操作（`===`, `!==`, `>`, `<`, `>=`, `<=`）
2. 数学运算操作（`+`, `-`, `*`, `/`, `%`）
3. 数组查找操作（`findIndex`, `indexOf`）
4. 对象属性访问（作为键值使用）
5. 条件判断中的使用

**执行步骤**：
1. 搜索所有使用这些字段的代码段
2. 分析代码中的操作类型
3. 评估类型变更的影响
4. 记录需要修改的代码位置

**验收标准**：
- [ ] 完成数值操作风险评估报告
- [ ] 标记所有需要修改的代码位置
- [ ] 提供修改建议

## 第四阶段：代码修改策略制定

### 任务 4.1：TypeScript类型定义检查
**目标**：检查和更新相关的TypeScript类型定义

**执行步骤**：
1. 搜索相关的接口类型定义
2. 检查是否存在明确的类型声明
3. 评估是否需要更新类型定义
4. 制定类型定义更新方案

**验收标准**：
- [ ] 完成类型定义检查报告
- [ ] 提供类型定义更新方案
- [ ] 确认是否需要代码逻辑修改

### 任务 4.2：代码逻辑修改方案
**目标**：针对需要修改的代码制定详细的修改方案

**修改原则**：
1. 保持向后兼容性
2. 最小化代码变更
3. 确保功能正确性
4. 添加必要的类型检查

**执行步骤**：
1. 针对每个需要修改的代码位置制定修改方案
2. 设计测试用例验证修改效果
3. 评估修改的影响范围
4. 制定回滚方案

**验收标准**：
- [ ] 完成代码修改方案文档
- [ ] 提供详细的测试场景
- [ ] 制定回滚预案

## 第五阶段：完整性检查和总结

### 任务 5.1：检查清单验证
**目标**：确保 todo.md 中的每个原始数据项都已经过检查

**执行步骤**：
1. 逐一核对 todo.md 中的152个数据项
2. 确认每个项目的检查状态
3. 补充遗漏的检查项目
4. 更新检查结果

**验收标准**：
- [ ] 100% 完成 todo.md 中所有项目的检查
- [ ] 每个项目都有明确的结论
- [ ] 更新 todo.md 中的"前端确认"列

### 任务 5.2：风险评估总结
**目标**：提供完整的风险评估报告和处理建议

**输出内容**：
1. 风险评估总结报告
2. 需要修改的代码清单
3. 测试验证方案
4. 上线部署建议

**验收标准**：
- [ ] 完成风险评估总结报告
- [ ] 提供明确的处理建议
- [ ] 制定测试和部署方案

## 执行时间安排

| 阶段 | 预计时间 | 关键里程碑 |
|------|----------|------------|
| 第一阶段 | 1天 | 完成接口匹配验证 |
| 第二阶段 | 2天 | 完成使用情况分析 |
| 第三阶段 | 2天 | 完成风险点识别 |
| 第四阶段 | 1天 | 完成修改方案制定 |
| 第五阶段 | 1天 | 完成总结报告 |

## 风险控制措施

1. **分批执行**：每个阶段完成后进行评审，确认无误后再进入下一阶段
2. **备份机制**：在进行任何代码修改前，确保有完整的代码备份
3. **测试验证**：每个修改都要有对应的测试用例
4. **回滚预案**：制定详细的回滚方案，确保可以快速恢复

## 具体检查要点

### 高风险代码模式
1. **数值比较**：`id === 123`, `targetId > 0`
2. **数学运算**：`id + 1`, `targetId * 2`
3. **数组索引**：`array[id]`, `list.findIndex(item => item.id === id)`
4. **对象键值**：`obj[id]`, `map.get(id)`
5. **条件判断**：`if (id)`, `id ? true : false`

### 安全代码模式
1. **字符串比较**：`id === "123"`, `targetId === item.targetId`
2. **字符串拼接**：`"prefix_" + id`
3. **显示用途**：`<span>{id}</span>`
4. **API参数**：直接作为请求参数传递

### 关键文件清单
基于分析，以下文件需要重点检查：

1. **接口定义文件**：
   - `src/lib/wkb-front.js` - 统计相关接口
   - `src/lib/wis-front.js` - 库存相关接口
   - `src/component/rfid-manage/rfid-query/server.js` - RFID接口
   - `src/component/inbound-manage/location-info-manage/server.js` - 库位接口

2. **业务组件文件**：
   - `src/component/rfid-manage/rfid-query/reducers.js` - RFID查询逻辑
   - `src/component/inbound-manage/location-info-manage/reducers.js` - 库位管理逻辑
   - `src/component/query/container-query/reducers.js` - 容器查询逻辑
   - `src/component/take-account/` - 盘点相关组件

3. **工具函数文件**：
   - `src/lib/util.js` - 通用工具函数
   - `src/component/take-account/data.js` - 盘点数据处理

## 测试验证方案

### 单元测试重点
1. **字段类型转换测试**：验证 long -> string 转换的正确性
2. **比较操作测试**：验证字符串比较的正确性
3. **查找操作测试**：验证数组查找功能的正确性
4. **条件判断测试**：验证逻辑判断的正确性

### 集成测试重点
1. **接口调用测试**：验证接口参数和返回值处理
2. **组件交互测试**：验证组件间数据传递
3. **业务流程测试**：验证完整业务流程的正确性

### 回归测试重点
1. **RFID查询功能**：扫描、查询、解绑、作废等操作
2. **库位管理功能**：查询、维护、商品采集等操作
3. **容器查询功能**：各种查询场景的验证
4. **统计看板功能**：数据展示和计算的正确性

## 注意事项

1. 重点关注数值比较和计算操作
2. 注意字符串和数字的隐式类型转换
3. 确保修改不影响现有功能
4. 保持代码的可读性和维护性
5. 所有修改都要有对应的测试用例
6. 修改前要做好代码备份和版本控制

## 执行检查表

### 阶段一检查表
- [ ] 环境变量配置确认完成
- [ ] 接口URL匹配验证完成
- [ ] 不匹配接口已记录并分析

### 阶段二检查表
- [ ] 接口函数定位完成
- [ ] 组件使用情况分析完成
- [ ] 高频接口已标记

### 阶段三检查表
- [ ] 受影响字段识别完成
- [ ] 数值操作风险评估完成
- [ ] 风险等级分类完成

### 阶段四检查表
- [ ] TypeScript类型定义检查完成
- [ ] 代码逻辑修改方案制定完成
- [ ] 测试用例设计完成

### 阶段五检查表
- [ ] todo.md 中所有项目检查完成
- [ ] 风险评估总结报告完成
- [ ] 测试和部署方案制定完成
